<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cpit.dadc.uhub</groupId>
        <artifactId>connector-uhub</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>connector-application</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.camunda.connector</groupId>
            <artifactId>spring-boot-starter-camunda-connectors</artifactId>
            <version>${version.connectors}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>io.camunda.connector</groupId>-->
<!--            <artifactId>connector-rabbitmq</artifactId>-->
<!--            <version>8.5.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.cpit.dadc.uhub</groupId>
            <artifactId>connector-http-json</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.camunda</groupId>
            <artifactId>zeebe-gateway</artifactId>
            <version>8.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>3.3.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
            <version>3.3.4</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.3.2</version>
                <!--                <configuration>-->
                <!--                    <mainClass>com.cpit.dadc.uhub.LocalConnectorRuntime</mainClass>-->
                <!--                    <skip>true</skip>-->
                <!--                </configuration>-->
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>