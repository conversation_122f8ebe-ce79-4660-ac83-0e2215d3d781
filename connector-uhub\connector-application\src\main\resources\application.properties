# Configuration for running connectors locally in bundle with connector-runtime
server.port=8080
zeebe.client.broker.gateway-address=************:26500
zeebe.client.security.plaintext=true

camunda.connector.polling.enabled=false
camunda.connector.polling.interval=1000
camunda.connector.webhook.enabled=false
operate.client.enabled=false

zeebe.client.default-job-worker-tenant-ids=1689136090481512449
#zeebe.client.default-tenant-id=1689136090481512449


spring.main.allow-bean-definition-overriding=true
#cpit.uhub.rest.extend-headers.c=aaa # rest connector extend headers
#logging.level.com.cpit.dadc.uhub.connector.http.rest=debug
