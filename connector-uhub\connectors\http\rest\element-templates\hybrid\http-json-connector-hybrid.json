{"$schema": "https://unpkg.com/@camunda/zeebe-element-templates-json-schema/resources/schema.json", "name": "REST Outbound Connector", "id": "io.camunda.connectors.HttpJson.v2", "description": "Invoke REST API", "documentationRef": "https://docs.camunda.io/docs/components/connectors/protocol/rest/", "version": 7, "category": {"id": "connectors", "name": "Connectors"}, "appliesTo": ["bpmn:Task"], "elementType": {"value": "bpmn:ServiceTask"}, "groups": [{"id": "taskDefinitionType", "label": "Task definition type"}, {"id": "authentication", "label": "Authentication"}, {"id": "endpoint", "label": "HTTP endpoint"}, {"id": "timeout", "label": "Connection timeout"}, {"id": "payload", "label": "Payload"}, {"id": "output", "label": "Output mapping"}, {"id": "error", "label": "Error handling"}, {"id": "retries", "label": "Retries"}], "properties": [{"id": "taskDefinitionType", "value": "io.camunda:http-json:1", "group": "taskDefinitionType", "binding": {"property": "type", "type": "zeebe:taskDefinition"}, "type": "String"}, {"id": "authentication.type", "label": "Type", "description": "Choose the authentication type. Select 'None' if no authentication is necessary", "value": "noAuth", "group": "authentication", "binding": {"name": "authentication.type", "type": "zeebe:input"}, "type": "Dropdown", "choices": [{"name": "API key", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Basic", "value": "basic"}, {"name": "Bearer token", "value": "bearer"}, {"name": "None", "value": "noAuth"}, {"name": "OAuth 2.0", "value": "oauth-client-credentials-flow"}]}, {"id": "authentication.apiKeyLocation", "label": "Api key location", "description": "Choose type: Send API key in header or as query parameter.", "optional": false, "value": "headers", "constraints": {"notEmpty": true}, "group": "authentication", "binding": {"name": "authentication.apiKeyLocation", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "<PERSON><PERSON><PERSON><PERSON>", "type": "simple"}, "type": "Dropdown", "choices": [{"name": "Headers", "value": "headers"}, {"name": "Query parameters", "value": "query"}]}, {"id": "authentication.name", "label": "API key name", "optional": false, "constraints": {"notEmpty": true}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.name", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "<PERSON><PERSON><PERSON><PERSON>", "type": "simple"}, "type": "String"}, {"id": "authentication.value", "label": "API key value", "optional": false, "constraints": {"notEmpty": true}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.value", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "<PERSON><PERSON><PERSON><PERSON>", "type": "simple"}, "type": "String"}, {"id": "authentication.username", "label": "Username", "optional": false, "constraints": {"notEmpty": true}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.username", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "basic", "type": "simple"}, "type": "String"}, {"id": "authentication.password", "label": "Password", "optional": false, "constraints": {"notEmpty": true}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.password", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "basic", "type": "simple"}, "type": "String"}, {"id": "authentication.token", "label": "Bearer token", "optional": false, "constraints": {"notEmpty": true}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.token", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "bearer", "type": "simple"}, "type": "String"}, {"id": "authentication.oauthTokenEndpoint", "label": "OAuth 2.0 token endpoint", "description": "The OAuth token endpoint", "optional": false, "constraints": {"notEmpty": true, "pattern": {"value": "^(=|http://|https://|secrets|\\{\\{).*$", "message": "Must be a http(s) URL"}}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.oauthTokenEndpoint", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "oauth-client-credentials-flow", "type": "simple"}, "type": "String"}, {"id": "authentication.clientId", "label": "Client ID", "description": "Your application's client ID from the OAuth client", "optional": false, "constraints": {"notEmpty": true}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.clientId", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "oauth-client-credentials-flow", "type": "simple"}, "type": "String"}, {"id": "authentication.clientSecret", "label": "Client secret", "description": "Your application's client secret from the OAuth client", "optional": false, "constraints": {"notEmpty": true}, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.clientSecret", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "oauth-client-credentials-flow", "type": "simple"}, "type": "String"}, {"id": "authentication.audience", "label": "Audience", "description": "The unique identifier of the target API you want to access", "optional": true, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.audience", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "oauth-client-credentials-flow", "type": "simple"}, "type": "String"}, {"id": "authentication.clientAuthentication", "label": "Client authentication", "description": "Send client ID and client secret as Basic Auth request in the header, or as client credentials in the request body", "optional": false, "constraints": {"notEmpty": true}, "group": "authentication", "binding": {"name": "authentication.clientAuthentication", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "oauth-client-credentials-flow", "type": "simple"}, "type": "Dropdown", "choices": [{"name": "Send client credentials in body", "value": "credentialsBody"}, {"name": "Send as Basic Auth header", "value": "basicAuthHeader"}]}, {"id": "authentication.scopes", "label": "<PERSON><PERSON><PERSON>", "description": "The scopes which you want to request authorization for (e.g.read:contacts)", "optional": true, "feel": "optional", "group": "authentication", "binding": {"name": "authentication.scopes", "type": "zeebe:input"}, "condition": {"property": "authentication.type", "equals": "oauth-client-credentials-flow", "type": "simple"}, "type": "String"}, {"id": "method", "label": "Method", "optional": false, "value": "GET", "constraints": {"notEmpty": true}, "group": "endpoint", "binding": {"name": "method", "type": "zeebe:input"}, "type": "Dropdown", "choices": [{"name": "DELETE", "value": "DELETE"}, {"name": "POST", "value": "POST"}, {"name": "GET", "value": "GET"}, {"name": "PATCH", "value": "PATCH"}, {"name": "PUT", "value": "PUT"}]}, {"id": "url", "label": "URL", "optional": false, "constraints": {"notEmpty": true, "pattern": {"value": "^(=|http://|https://|secrets|\\{\\{).*$", "message": "Must be a http(s) URL"}}, "feel": "optional", "group": "endpoint", "binding": {"name": "url", "type": "zeebe:input"}, "type": "String"}, {"id": "headers", "label": "Headers", "description": "Map of HTTP headers to add to the request", "optional": true, "feel": "required", "group": "endpoint", "binding": {"name": "headers", "type": "zeebe:input"}, "type": "String"}, {"id": "queryParameters", "label": "Query parameters", "description": "Map of query parameters to add to the request URL", "optional": true, "feel": "required", "group": "endpoint", "binding": {"name": "queryParameters", "type": "zeebe:input"}, "type": "String"}, {"id": "connectionTimeoutInSeconds", "label": "Connection timeout in seconds", "description": "Defines the connection timeout in seconds, or 0 for an infinite timeout", "optional": false, "value": "20", "constraints": {"notEmpty": true}, "feel": "optional", "group": "timeout", "binding": {"name": "connectionTimeoutInSeconds", "type": "zeebe:input"}, "type": "String"}, {"id": "readTimeoutInSeconds", "label": "Read timeout in seconds", "description": "Timeout in seconds to read data from an established connection or 0 for an infinite timeout", "optional": false, "value": "20", "constraints": {"notEmpty": true}, "feel": "optional", "group": "timeout", "binding": {"name": "readTimeoutInSeconds", "type": "zeebe:input"}, "type": "String"}, {"id": "writeTimeoutInSeconds", "label": "Write timeout in seconds", "description": "Timeout in seconds to set data or 0 for an infinite timeout", "optional": false, "value": "0", "constraints": {"notEmpty": true}, "feel": "optional", "group": "timeout", "binding": {"name": "writeTimeoutInSeconds", "type": "zeebe:input"}, "condition": {"property": "method", "oneOf": ["POST", "PUT", "PATCH"], "type": "simple"}, "type": "String"}, {"id": "body", "label": "Request body", "description": "Payload to send with the request", "optional": true, "feel": "optional", "group": "payload", "binding": {"name": "body", "type": "zeebe:input"}, "condition": {"property": "method", "oneOf": ["POST", "PUT", "PATCH"], "type": "simple"}, "type": "Text"}, {"id": "resultVariable", "label": "Result variable", "description": "Name of variable to store the response in", "group": "output", "binding": {"key": "resultVariable", "type": "zeebe:task<PERSON>eader"}, "type": "String"}, {"id": "resultExpression", "label": "Result expression", "description": "Expression to map the response into process variables", "feel": "required", "group": "output", "binding": {"key": "resultExpression", "type": "zeebe:task<PERSON>eader"}, "type": "Text"}, {"id": "errorExpression", "label": "Error expression", "description": "Expression to handle errors. Details in the <a href=\"https://docs.camunda.io/docs/components/connectors/use-connectors/\" target=\"_blank\">documentation</a>.", "feel": "required", "group": "error", "binding": {"key": "errorExpression", "type": "zeebe:task<PERSON>eader"}, "type": "Text"}, {"id": "retryCount", "label": "Retries", "description": "Number of retries", "value": "3", "feel": "optional", "group": "retries", "binding": {"property": "retries", "type": "zeebe:taskDefinition"}, "type": "String"}, {"id": "re<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> backoff", "description": "ISO-8601 duration to wait between retries", "value": "PT0S", "feel": "optional", "group": "retries", "binding": {"key": "re<PERSON><PERSON><PERSON><PERSON>", "type": "zeebe:task<PERSON>eader"}, "type": "String"}], "icon": {"contents": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE3LjAzMzUgOC45OTk5N0MxNy4wMzM1IDEzLjQ0NzUgMTMuNDI4MSAxNy4wNTI5IDguOTgwNjUgMTcuMDUyOUM0LjUzMzE2IDE3LjA1MjkgMC45Mjc3NjUgMTMuNDQ3NSAwLjkyNzc2NSA4Ljk5OTk3QzAuOTI3NzY1IDQuNTUyNDggNC41MzMxNiAwLjk0NzA4MyA4Ljk4MDY1IDAuOTQ3MDgzQzEzLjQyODEgMC45NDcwODMgMTcuMDMzNSA0LjU1MjQ4IDE3LjAzMzUgOC45OTk5N1oiIGZpbGw9IiM1MDU1NjIiLz4KPHBhdGggZD0iTTQuOTMxMjYgMTQuMTU3MUw2Ljc4MTA2IDMuNzE0NzFIMTAuMTM3NUMxMS4xOTE3IDMuNzE0NzEgMTEuOTgyNCAzLjk4MzIzIDEyLjUwOTUgNC41MjAyN0MxMy4wNDY1IDUuMDQ3MzYgMTMuMzE1IDUuNzMzNTggMTMuMzE1IDYuNTc4OTJDMTMuMzE1IDcuNDQ0MTQgMTMuMDcxNCA4LjE1NTIyIDEyLjU4NDEgOC43MTIxNUMxMi4xMDY3IDkuMjU5MTMgMTEuNDU1MyA5LjYzNzA1IDEwLjYyOTggOS44NDU5TDEyLjA2MTkgMTQuMTU3MUgxMC4zMzE1TDkuMDMzNjQgMTAuMDI0OUg3LjI0MzUxTDYuNTEyNTQgMTQuMTU3MUg0LjkzMTI2Wk03LjQ5NzExIDguNTkyODFIOS4yNDI0OEM5Ljk5ODMyIDguNTkyODEgMTAuNTkwMSA4LjQyMzc0IDExLjAxNzcgOC4wODU2MUMxMS40NTUzIDcuNzM3NTMgMTEuNjc0MSA3LjI2NTEzIDExLjY3NDEgNi42Njg0MkMxMS42NzQxIDYuMTkxMDYgMTEuNTI0OSA1LjgxODExIDExLjIyNjUgNS41NDk1OUMxMC45MjgyIDUuMjcxMTMgMTAuNDU1OCA1LjEzMTkgOS44MDkzNiA1LjEzMTlIOC4xMDg3NEw3LjQ5NzExIDguNTkyODFaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K"}}