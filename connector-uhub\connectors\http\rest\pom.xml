<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cpit.dadc.uhub</groupId>
        <artifactId>connector-http-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <name>connector-http-json</name>
    <description>Camunda Cloud HTTP JSON Connector</description>
    <artifactId>connector-http-json</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <license.inlineheader>Copyright Camunda Services GmbH and/or licensed to Camunda Services GmbH
            under one or more contributor license agreements. See the NOTICE file
            distributed with this work for additional information regarding copyright
            ownership. Camunda licenses this file to you under the Apache License,
            Version 2.0; you may not use this file except in compliance with the License.
            You may obtain a copy of the License at

            http://www.apache.org/licenses/LICENSE-2.0

            Unless required by applicable law or agreed to in writing, software
            distributed under the License is distributed on an "AS IS" BASIS,
            WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
            See the License for the specific language governing permissions and
            limitations under the License.</license.inlineheader>

        <version.system-stubs>2.1.6</version.system-stubs>
        <element-template-generator-maven-plugin.version>8.5.0</element-template-generator-maven-plugin.version>
        <connector-http-base.version>8.5.0</connector-http-base.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.camunda.connector</groupId>
            <artifactId>connector-http-base</artifactId>
            <version>${connector-http-base.version}</version>
        </dependency>

        <dependency>
            <groupId>org.danilopianini</groupId>
            <artifactId>gson-extras</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.google.http-client</groupId>-->
<!--            <artifactId>google-http-client-gson</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.google.http-client</groupId>-->
<!--            <artifactId>google-http-client-apache-v2</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.google.auth</groupId>
            <artifactId>google-auth-library-oauth2-http</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.slf4j</groupId>-->
<!--            <artifactId>jcl-over-slf4j</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>uk.org.webcompere</groupId>
            <artifactId>system-stubs-jupiter</artifactId>
            <version>${version.system-stubs}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>io.camunda.connector</groupId>
                <artifactId>element-template-generator-maven-plugin</artifactId>
                <version>${element-template-generator-maven-plugin.version}</version>
                <configuration>
                    <connectors>
                        <connector>
                            <connectorClass>io.camunda.connector.http.com.cpit.dadc.uhub.connector.http.rest.HttpJsonFunction</connectorClass>
                            <files>
                                <file>
                                    <templateId>io.camunda.connectors.HttpJson.v2</templateId>
                                    <templateFileName>http-json-connector.json</templateFileName>
                                </file>
                            </files>
                            <generateHybridTemplates>true</generateHybridTemplates>
                        </connector>
                    </connectors>
                    <includeDependencies>
                        <includeDependency>io.camunda.connector:connector-http-base</includeDependency>
                    </includeDependencies>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>cloud-function-plain</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.functions</groupId>
                        <artifactId>function-maven-plugin</artifactId>
                        <configuration>
                            <functionTarget>io.camunda.connector.runtime.cloud.PlainCloudConnectorFunction
                            </functionTarget>
                            <port>9083</port>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>