/*
 * Copyright Camunda Services GmbH and/or licensed to Camunda Services GmbH
 * under one or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information regarding copyright
 * ownership. Camunda licenses this file to you under the Apache License,
 * Version 2.0; you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.cpit.dadc.uhub.connector.http.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.http.HttpRequestFactory;
import io.camunda.connector.api.annotation.OutboundConnector;
import io.camunda.connector.api.json.ConnectorsObjectMapperSupplier;
import io.camunda.connector.api.outbound.OutboundConnectorContext;
import io.camunda.connector.api.outbound.OutboundConnectorFunction;
import io.camunda.connector.generator.java.annotation.ElementTemplate;
import io.camunda.connector.generator.java.annotation.ElementTemplate.PropertyGroup;
import io.camunda.connector.http.base.components.HttpTransportComponentSupplier;
import io.camunda.connector.http.base.model.HttpCommonRequest;
import io.camunda.connector.http.base.services.HttpService;
import com.cpit.dadc.uhub.connector.http.rest.model.HttpJsonRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@OutboundConnector(
    name = "HTTP REST",
    inputVariables = {
      "url",
      "method",
      "authentication",
      "headers",
      "queryParameters",
      "connectionTimeoutInSeconds",
      "readTimeoutInSeconds",
      "writeTimeoutInSeconds",
      "body"
    },
    type = HttpJsonFunction.TYPE)
@ElementTemplate(
    id = "io.camunda.connectors.HttpJson.v2",
    name = "REST Outbound Connector",
    description = "Invoke REST API",
    inputDataClass = HttpJsonRequest.class,
    version = 7,
    propertyGroups = {
      @PropertyGroup(id = "authentication", label = "Authentication"),
      @PropertyGroup(id = "endpoint", label = "HTTP endpoint"),
      @PropertyGroup(id = "timeout", label = "Connection timeout"),
      @PropertyGroup(id = "payload", label = "Payload")
    },
    documentationRef = "https://docs.camunda.io/docs/components/connectors/protocol/rest/",
    icon = "icon.svg")
public class HttpJsonFunction implements OutboundConnectorFunction {
  public static final String TYPE = "io.camunda:http-json:1";
  private static final Logger log = LoggerFactory.getLogger(HttpJsonFunction.class);

  private final String extendHeadersStart = "cpit.uhub.rest.extend-headers.";
  private final HttpService httpService;

  public HttpJsonFunction() {
    this(
        ConnectorsObjectMapperSupplier.getCopy(),
        HttpTransportComponentSupplier.httpRequestFactoryInstance());
  }

  public HttpJsonFunction(
      final ObjectMapper objectMapper, final HttpRequestFactory requestFactory) {
    this.httpService = new HttpService(objectMapper, requestFactory);
  }

  @Override
  public Object execute(final OutboundConnectorContext context)
      throws IOException, InstantiationException, IllegalAccessException {
    final var request = context.bindVariables(HttpJsonRequest.class);
    return httpService.executeConnectorRequest(addExtendHeaders(request));
  }

  private HttpCommonRequest addExtendHeaders(HttpCommonRequest request) {
    Map<String, String> extendHeaders = getExtendHeaders();
    log.debug("rest extendHeaders: {}", extendHeaders);
    Map<String, String> headers = request.getHeaders();
    if (headers == null) headers = new HashMap<>();
    if (!extendHeaders.isEmpty()) headers.putAll(extendHeaders);
    request.setHeaders(headers);
    return request;
  }

  private Map<String, String> getExtendHeaders() {
    Properties properties = new Properties();
    String filePath = "application.properties";
    File file = new File(filePath);
    if (file.exists()) {
      try (FileInputStream input = new FileInputStream(filePath)) {
        if (input == null) {
          throw new RuntimeException("Unable to find application.properties");
        }
        // 加载配置文件
        properties.load(input);
      } catch (Exception ex) {
        throw new RuntimeException("Error loading configuration", ex);
      }
    } else {
      try (InputStream input =
                   getClass().getClassLoader().getResourceAsStream("application.properties")) {
        if (input == null) {
          throw new RuntimeException("Unable to find config.properties");
        }
        // 加载配置文件
        properties.load(input);
      } catch (Exception ex) {
        throw new RuntimeException("Error loading configuration", ex);
      }
    }
    Map<String, String> extendHeaders = new HashMap<>();
    properties.forEach(
        (k, v) -> {
          String key = k.toString();
          if (key.startsWith(extendHeadersStart)) {
            extendHeaders.put(key.replace(extendHeadersStart, ""), v.toString());
          }
        });
    return extendHeaders;
  }
}
