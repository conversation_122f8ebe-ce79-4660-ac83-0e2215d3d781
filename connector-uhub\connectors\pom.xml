<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cpit.dadc.uhub</groupId>
        <artifactId>connector-uhub</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>connectors-parent</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>http</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <version.gson-extras>1.2.0</version.gson-extras>
        <plugin.version.maven-exec-plugin>3.2.0</plugin.version.maven-exec-plugin>
        <version.google-auth-library-oauth2-http>1.23.0</version.google-auth-library-oauth2-http>
        <version.httpcore>4.4.16</version.httpcore>

    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.danilopianini</groupId>
                <artifactId>gson-extras</artifactId>
                <version>${version.gson-extras}</version>
            </dependency>
            <dependency>
                <groupId>com.google.auth</groupId>
                <artifactId>google-auth-library-oauth2-http</artifactId>
                <version>${version.google-auth-library-oauth2-http}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${version.httpcore}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>