
-- ----------------------------
-- Table structure for act_decision
-- ----------------------------
DROP TABLE IF EXISTS "act_decision";
CREATE TABLE "act_decision" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "decision_key" int8 NOT NULL,
  "decision_id" varchar(255) COLLATE "pg_catalog"."default",
  "decision_name" varchar(255) COLLATE "pg_catalog"."default",
  "version" int4,
  "decision_requirements_key" int8,
  "decision_requirements_id" varchar(255) COLLATE "pg_catalog"."default",
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_decision_evaluation
-- ----------------------------
DROP TABLE IF EXISTS "act_decision_evaluation";
CREATE TABLE "act_decision_evaluation" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "key" int8 NOT NULL,
  "execution_index" int4,
  "state" varchar(255) COLLATE "pg_catalog"."default",
  "evaluation_date" timestamp(6),
  "evaluation_failure" text COLLATE "pg_catalog"."default",
  "position" int8,
  "partition_id" int4,
  "decision_requirements_key" int8,
  "decision_requirements_id" varchar(255) COLLATE "pg_catalog"."default",
  "process_definition_key" int8,
  "process_instance_key" int8,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default",
  "element_instance_key" int8,
  "element_id" varchar(255) COLLATE "pg_catalog"."default",
  "decision_id" varchar(255) COLLATE "pg_catalog"."default",
  "decision_definition_id" varchar(255) COLLATE "pg_catalog"."default",
  "decision_name" varchar(255) COLLATE "pg_catalog"."default",
  "decision_version" int4,
  "root_decision_name" varchar(255) COLLATE "pg_catalog"."default",
  "root_decision_id" varchar(255) COLLATE "pg_catalog"."default",
  "root_decision_definition_id" varchar(255) COLLATE "pg_catalog"."default",
  "decision_type" varchar(255) COLLATE "pg_catalog"."default",
  "result" text COLLATE "pg_catalog"."default",
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_decision_evaluation_input
-- ----------------------------
DROP TABLE IF EXISTS "act_decision_evaluation_input";
CREATE TABLE "act_decision_evaluation_input" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "input_id" varchar(255) COLLATE "pg_catalog"."default",
  "input_name" varchar(255) COLLATE "pg_catalog"."default",
  "value_" text COLLATE "pg_catalog"."default",
  "evaluated_decision_id" varchar(255) COLLATE "pg_catalog"."default",
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_decision_evaluation_output
-- ----------------------------
DROP TABLE IF EXISTS "act_decision_evaluation_output";
CREATE TABLE "act_decision_evaluation_output" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "output_id" varchar(255) COLLATE "pg_catalog"."default",
  "output_name" varchar(255) COLLATE "pg_catalog"."default",
  "value_" text COLLATE "pg_catalog"."default",
  "evaluated_decision_id" varchar(255) COLLATE "pg_catalog"."default",
  "rule_id" varchar(255) COLLATE "pg_catalog"."default",
  "rule_index" int4,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_decision_requirements
-- ----------------------------
DROP TABLE IF EXISTS "act_decision_requirements";
CREATE TABLE "act_decision_requirements" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "decision_requirements_key" int8,
  "decision_requirements_id" varchar(255) COLLATE "pg_catalog"."default",
  "decision_requirements_name" varchar(255) COLLATE "pg_catalog"."default",
  "version" int4,
  "namespace" varchar(255) COLLATE "pg_catalog"."default",
  "dmn_xml" text COLLATE "pg_catalog"."default",
  "resource_name" varchar(255) COLLATE "pg_catalog"."default",
  "checksum" varchar(255) COLLATE "pg_catalog"."default",
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_element_instance_state_transition
-- ----------------------------
DROP TABLE IF EXISTS "act_element_instance_state_transition";
CREATE TABLE "act_element_instance_state_transition" (
  "partition_id_with_position" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "element_instance_key" int8,
  "state" varchar(50) COLLATE "pg_catalog"."default",
  "timestamp" int8
)
;

-- ----------------------------
-- Table structure for act_error
-- ----------------------------
DROP TABLE IF EXISTS "act_error";
CREATE TABLE "act_error" (
  "position" int8 NOT NULL,
  "error_event_position" int8,
  "exception_message" text COLLATE "pg_catalog"."default",
  "stacktrace" text COLLATE "pg_catalog"."default",
  "process_instance_key" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_event
-- ----------------------------
DROP TABLE IF EXISTS "act_event";
CREATE TABLE "act_event" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "key" int8 NOT NULL,
  "partition_id" int4 NOT NULL,
  "process_definition_key" int8,
  "process_instance_key" int8,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default",
  "flow_node_id" varchar(255) COLLATE "pg_catalog"."default",
  "flow_node_instance_key" int8,
  "event_source_type" varchar(255) COLLATE "pg_catalog"."default",
  "event_type" varchar(255) COLLATE "pg_catalog"."default",
  "date_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default",
  "position" int8,
  "position_incident" int8,
  "position_process_message_subscription" int8,
  "position_job" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "event_id" varchar COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_event_meta_data
-- ----------------------------
DROP TABLE IF EXISTS "act_event_meta_data";
CREATE TABLE "act_event_meta_data" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "event_id" varchar(255) COLLATE "pg_catalog"."default",
  "job_type" varchar(255) COLLATE "pg_catalog"."default",
  "job_retries" int4,
  "job_worker" varchar(255) COLLATE "pg_catalog"."default",
  "job_deadline" timestamp(6),
  "job_custom_headers_json" text COLLATE "pg_catalog"."default",
  "job_key" int8,
  "incident_error_type" varchar(255) COLLATE "pg_catalog"."default",
  "incident_error_message" text COLLATE "pg_catalog"."default",
  "message_name" varchar(255) COLLATE "pg_catalog"."default",
  "correlation_key" varchar(255) COLLATE "pg_catalog"."default",
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_flow_node_instance
-- ----------------------------
DROP TABLE IF EXISTS "act_flow_node_instance";
CREATE TABLE "act_flow_node_instance" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "flow_node_instance_id" int8 NOT NULL,
  "flow_node_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_flow_node_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "process_instance_key" int8 NOT NULL,
  "process_definition_key" int8 NOT NULL,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "start_date" int8,
  "end_date" int8,
  "incident_key" int8,
  "partition_id" int8,
  "tree_path" varchar(255) COLLATE "pg_catalog"."default",
  "level" int4,
  "incident" bool,
  "sort_values" varchar(255) COLLATE "pg_catalog"."default",
  "position" int8 NOT NULL,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(3),
  "update_time" timestamp(3),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default",
  "processed" bool NOT NULL DEFAULT false
)
;
COMMENT ON COLUMN "act_flow_node_instance"."flow_node_instance_id" IS '流程实例节点id';
COMMENT ON COLUMN "act_flow_node_instance"."flow_node_id" IS '流程元素的唯一标识符';
COMMENT ON COLUMN "act_flow_node_instance"."parent_flow_node_id" IS '当前节点的父节点的唯一标识符';
COMMENT ON COLUMN "act_flow_node_instance"."process_instance_key" IS '流程实例key';
COMMENT ON COLUMN "act_flow_node_instance"."process_definition_key" IS '流程定义key，与process_definition_id一致';
COMMENT ON COLUMN "act_flow_node_instance"."bpmn_process_id" IS 'BPMN流程ID';
COMMENT ON COLUMN "act_flow_node_instance"."state" IS '节点状态';
COMMENT ON COLUMN "act_flow_node_instance"."type" IS '事件类型';
COMMENT ON COLUMN "act_flow_node_instance"."start_date" IS '开始时间';
COMMENT ON COLUMN "act_flow_node_instance"."end_date" IS '结束时间';
COMMENT ON COLUMN "act_flow_node_instance"."incident_key" IS '事件键，事件的唯一标识符，通常用于标识异常或问题';
COMMENT ON COLUMN "act_flow_node_instance"."partition_id" IS '分区 ID';
COMMENT ON COLUMN "act_flow_node_instance"."tree_path" IS '用于表示节点在树结构中的路径';
COMMENT ON COLUMN "act_flow_node_instance"."incident" IS '是否异常';
COMMENT ON COLUMN "act_flow_node_instance"."sort_values" IS '用于排序的值数组';
COMMENT ON COLUMN "act_flow_node_instance"."position" IS '偏移量，单调递增，用于标识事件的顺序和唯一性';
COMMENT ON COLUMN "act_flow_node_instance"."tenant_id" IS '租户';
COMMENT ON COLUMN "act_flow_node_instance"."processed" IS '是否已处理（默认未处理）';

-- ----------------------------
-- Table structure for act_form
-- ----------------------------
DROP TABLE IF EXISTS "act_form";
CREATE TABLE "act_form" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "bpmn_id" varchar(255) COLLATE "pg_catalog"."default",
  "process_definition_id" int8,
  "schema" text COLLATE "pg_catalog"."default",
  "version" int8,
  "embedded" bool,
  "is_deleted" bool,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_ge_property
-- ----------------------------
DROP TABLE IF EXISTS "act_ge_property";
CREATE TABLE "act_ge_property" (
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "act_ge_property"."name" IS '属性名称';
COMMENT ON COLUMN "act_ge_property"."value" IS '属性值';
COMMENT ON TABLE "act_ge_property" IS '引擎集群属性表';

-- ----------------------------
-- Table structure for act_incident
-- ----------------------------
DROP TABLE IF EXISTS "act_incident";
CREATE TABLE "act_incident" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "key" int8 NOT NULL,
  "error_type" varchar(255) COLLATE "pg_catalog"."default",
  "error_message" text COLLATE "pg_catalog"."default",
  "error_message_hash" int4,
  "state" varchar(50) COLLATE "pg_catalog"."default",
  "flow_node_id" varchar(255) COLLATE "pg_catalog"."default",
  "flow_node_instance_key" int8,
  "job_key" int8,
  "process_instance_key" int8,
  "creation_time" int8,
  "process_definition_key" int8,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default",
  "tree_path" varchar(255) COLLATE "pg_catalog"."default",
  "partition_id" int4,
  "position" int8,
  "pending" bool,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "act_incident"."id" IS '主键，唯一标识符';
COMMENT ON COLUMN "act_incident"."key" IS '唯一标识键，用于业务逻辑的唯一标识';
COMMENT ON COLUMN "act_incident"."error_type" IS '错误类型，表示事故的类型（如 IO_MAPPING_ERROR）';
COMMENT ON COLUMN "act_incident"."error_message" IS '错误信息，描述事故的具体原因';
COMMENT ON COLUMN "act_incident"."error_message_hash" IS '错误信息的哈希值，用于快速比较或索引';
COMMENT ON COLUMN "act_incident"."state" IS '状态，表示事故的当前状态（如 CREATED、RESOLVED 等）';
COMMENT ON COLUMN "act_incident"."flow_node_id" IS '流程节点ID，表示事故发生的流程节点';
COMMENT ON COLUMN "act_incident"."flow_node_instance_key" IS '流程节点实例键，表示事故发生的流程节点实例';
COMMENT ON COLUMN "act_incident"."job_key" IS '作业键，表示与事故相关的作业';
COMMENT ON COLUMN "act_incident"."process_instance_key" IS '流程实例键，表示事故所属的流程实例';
COMMENT ON COLUMN "act_incident"."creation_time" IS '创建时间，表示事故的创建时间（时间戳）';
COMMENT ON COLUMN "act_incident"."process_definition_key" IS '流程定义键，表示事故所属的流程定义';
COMMENT ON COLUMN "act_incident"."bpmn_process_id" IS 'BPMN流程ID，表示事故所属的BPMN流程';
COMMENT ON COLUMN "act_incident"."tree_path" IS '树路径，表示事故在流程树中的路径';
COMMENT ON COLUMN "act_incident"."partition_id" IS '分区ID，表示事故所属的分区';
COMMENT ON COLUMN "act_incident"."position" IS '位置，表示事故在日志中的位置';
COMMENT ON COLUMN "act_incident"."pending" IS '是否挂起，表示事故是否处于挂起状态';
COMMENT ON COLUMN "act_incident"."create_by" IS '创建人，表示创建事故的用户';
COMMENT ON COLUMN "act_incident"."update_by" IS '更新人，表示最后更新事故的用户';
COMMENT ON COLUMN "act_incident"."create_time" IS '创建时间，表示事故的创建时间（日期时间）';
COMMENT ON COLUMN "act_incident"."update_time" IS '更新时间，表示事故的最后更新时间（日期时间）';
COMMENT ON COLUMN "act_incident"."tenant_id" IS '租户ID，表示事故所属的租户';

-- ----------------------------
-- Table structure for act_message
-- ----------------------------
DROP TABLE IF EXISTS "act_message";
CREATE TABLE "act_message" (
  "key" int8 NOT NULL,
  "position" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "correlation_key" varchar(255) COLLATE "pg_catalog"."default",
  "message_id" varchar(255) COLLATE "pg_catalog"."default",
  "time_to_live" int8,
  "state" varchar(50) COLLATE "pg_catalog"."default" DEFAULT 'PUBLISHED'::character varying,
  "timestamp" int8 DEFAULT '-1'::integer,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_message_correlation
-- ----------------------------
DROP TABLE IF EXISTS "act_message_correlation";
CREATE TABLE "act_message_correlation" (
  "partition_id_with_position" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "message_key" int8,
  "message_name" varchar(255) COLLATE "pg_catalog"."default",
  "timestamp" int8,
  "process_instance_key" int8,
  "element_instance_key" int8,
  "element_id" varchar(255) COLLATE "pg_catalog"."default",
  "process_definition_key" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_message_subscription
-- ----------------------------
DROP TABLE IF EXISTS "act_message_subscription";
CREATE TABLE "act_message_subscription" (
  "key_" int8 NOT NULL,
  "position" int8,
  "message_name" varchar(255) COLLATE "pg_catalog"."default",
  "message_correlation_key" varchar(255) COLLATE "pg_catalog"."default",
  "process_instance_key" int8,
  "element_instance_key" int8,
  "process_definition_key" int8,
  "element_id" varchar(255) COLLATE "pg_catalog"."default",
  "state" varchar(50) COLLATE "pg_catalog"."default" DEFAULT 'CREATED'::character varying,
  "timestamp" int8 DEFAULT '-1'::integer,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_message_variable
-- ----------------------------
DROP TABLE IF EXISTS "act_message_variable";
CREATE TABLE "act_message_variable" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "value" text COLLATE "pg_catalog"."default",
  "message_key" int8,
  "position" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_process
-- ----------------------------
DROP TABLE IF EXISTS "act_process";
CREATE TABLE "act_process" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "process_definition_key" int8 NOT NULL,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "version" int4 NOT NULL,
  "bpmn_xml" text COLLATE "pg_catalog"."default" NOT NULL,
  "deploy_time" timestamp(6) NOT NULL,
  "resource_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "checksum" varchar(255) COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "started_by_form" bool,
  "form_key" varchar(255) COLLATE "pg_catalog"."default",
  "form_id" varchar(255) COLLATE "pg_catalog"."default",
  "is_form_embedded" bool,
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "act_process"."id" IS '主键id';
COMMENT ON COLUMN "act_process"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "act_process"."bpmn_process_id" IS 'BPMN流程ID';
COMMENT ON COLUMN "act_process"."version" IS '版本';
COMMENT ON COLUMN "act_process"."bpmn_xml" IS '流程定义内容';
COMMENT ON COLUMN "act_process"."deploy_time" IS '部署时间';
COMMENT ON COLUMN "act_process"."resource_name" IS '资源名';
COMMENT ON COLUMN "act_process"."checksum" IS '校验和';
COMMENT ON COLUMN "act_process"."name" IS '流程名称';
COMMENT ON COLUMN "act_process"."started_by_form" IS '是否通过表单启动';
COMMENT ON COLUMN "act_process"."form_key" IS '表单键	流程启动时关联的表单的唯一标识符';
COMMENT ON COLUMN "act_process"."form_id" IS '表单 ID';
COMMENT ON COLUMN "act_process"."is_form_embedded" IS '是否嵌入表单';
COMMENT ON COLUMN "act_process"."create_time" IS '创建时间';
COMMENT ON COLUMN "act_process"."update_time" IS '更新时间';
COMMENT ON COLUMN "act_process"."tenant_id" IS '租户id';

-- ----------------------------
-- Table structure for act_process_flow_node
-- ----------------------------
DROP TABLE IF EXISTS "act_process_flow_node";
CREATE TABLE "act_process_flow_node" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "flow_node_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "process_definition_key" int8 NOT NULL,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "act_process_flow_node"."id" IS '主键id';
COMMENT ON COLUMN "act_process_flow_node"."flow_node_id" IS '流程元素的唯一标识符';
COMMENT ON COLUMN "act_process_flow_node"."name" IS '流程元素的名称';
COMMENT ON COLUMN "act_process_flow_node"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "act_process_flow_node"."create_by" IS '创建人';
COMMENT ON COLUMN "act_process_flow_node"."update_by" IS '更新人';
COMMENT ON COLUMN "act_process_flow_node"."create_time" IS '创建时间';
COMMENT ON COLUMN "act_process_flow_node"."update_time" IS '更新时间';
COMMENT ON COLUMN "act_process_flow_node"."tenant_id" IS '租户';

-- ----------------------------
-- Table structure for act_process_instance
-- ----------------------------
DROP TABLE IF EXISTS "act_process_instance";
CREATE TABLE "act_process_instance" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "key" int8 NOT NULL,
  "process_instance_key" int8 NOT NULL,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "process_definition_key" int8 NOT NULL,
  "parent_process_instance_key" int8,
  "parent_element_instance_key" int8,
  "version" int4 NOT NULL,
  "start_date" int8,
  "end_date" int8,
  "position" int8,
  "partition_id" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "act_process_instance"."id" IS '主键';
COMMENT ON COLUMN "act_process_instance"."key" IS 'zeebe主键key';
COMMENT ON COLUMN "act_process_instance"."process_instance_key" IS '流程定义key';
COMMENT ON COLUMN "act_process_instance"."bpmn_process_id" IS 'BPMN流程ID';
COMMENT ON COLUMN "act_process_instance"."state" IS '流程实例状态';
COMMENT ON COLUMN "act_process_instance"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "act_process_instance"."parent_process_instance_key" IS '父流程定义key';
COMMENT ON COLUMN "act_process_instance"."parent_element_instance_key" IS '父元素实例key';
COMMENT ON COLUMN "act_process_instance"."version" IS '版本';
COMMENT ON COLUMN "act_process_instance"."start_date" IS '开始时间';
COMMENT ON COLUMN "act_process_instance"."end_date" IS '结束时间';
COMMENT ON COLUMN "act_process_instance"."position" IS '偏移量,单调递增，用于标识事件的顺序和唯一性';
COMMENT ON COLUMN "act_process_instance"."partition_id" IS '分区id';
COMMENT ON COLUMN "act_process_instance"."create_by" IS '创建人';
COMMENT ON COLUMN "act_process_instance"."update_by" IS '更新人';
COMMENT ON COLUMN "act_process_instance"."create_time" IS '创建时间';
COMMENT ON COLUMN "act_process_instance"."update_time" IS '更新时间';
COMMENT ON COLUMN "act_process_instance"."tenant_id" IS '租户';

-- ----------------------------
-- Table structure for act_sequence_flow
-- ----------------------------
DROP TABLE IF EXISTS "act_sequence_flow";
CREATE TABLE "act_sequence_flow" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "process_instance_key" int8 NOT NULL,
  "process_definition_key" int8 NOT NULL,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "activity_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_signal
-- ----------------------------
DROP TABLE IF EXISTS "act_signal";
CREATE TABLE "act_signal" (
  "key_" int8 NOT NULL,
  "position" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "timestamp" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_signal_subscription
-- ----------------------------
DROP TABLE IF EXISTS "act_signal_subscription";
CREATE TABLE "act_signal_subscription" (
  "key_" int8 NOT NULL,
  "position" int8,
  "signal_name" varchar(255) COLLATE "pg_catalog"."default",
  "process_definition_key" int8,
  "element_id" varchar(255) COLLATE "pg_catalog"."default",
  "state" varchar(50) COLLATE "pg_catalog"."default" DEFAULT 'CREATED'::character varying,
  "timestamp" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_signal_variable
-- ----------------------------
DROP TABLE IF EXISTS "act_signal_variable";
CREATE TABLE "act_signal_variable" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "value" text COLLATE "pg_catalog"."default",
  "signal_key" int8,
  "position" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_task
-- ----------------------------
DROP TABLE IF EXISTS "act_task";
CREATE TABLE "act_task" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "task_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "process_instance_key" int8 NOT NULL,
  "job_type" varchar(255) COLLATE "pg_catalog"."default",
  "state" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "due_date" timestamp(6),
  "creation_time" int8,
  "completion_time" int8,
  "assignee" varchar(255) COLLATE "pg_catalog"."default",
  "assigned" bool,
  "candidate_groups" varchar(255) COLLATE "pg_catalog"."default",
  "candidate_users" varchar(255) COLLATE "pg_catalog"."default",
  "retries" int4,
  "worker" varchar(255) COLLATE "pg_catalog"."default",
  "process_definition_key" int8 NOT NULL,
  "process_definition_version" int4 NOT NULL,
  "bpmn_process_id" int8 NOT NULL,
  "flow_node_bpmn_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "flow_node_instance_id" int8 NOT NULL,
  "form_key" varchar(255) COLLATE "pg_catalog"."default",
  "form_id" varchar(255) COLLATE "pg_catalog"."default",
  "form_version" int8,
  "is_form_embedded" bool,
  "follow_up_date" timestamp(6),
  "implementation" varchar(50) COLLATE "pg_catalog"."default",
  "position" int8,
  "partition_id" int4,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "act_task"."id" IS '主键id';
COMMENT ON COLUMN "act_task"."task_id" IS '任务id';
COMMENT ON COLUMN "act_task"."process_instance_key" IS '流程实例key';
COMMENT ON COLUMN "act_task"."job_type" IS '任务类型';
COMMENT ON COLUMN "act_task"."state" IS '任务状态';
COMMENT ON COLUMN "act_task"."due_date" IS '截止日期';
COMMENT ON COLUMN "act_task"."creation_time" IS '任务创建时间';
COMMENT ON COLUMN "act_task"."completion_time" IS '任务完成时间';
COMMENT ON COLUMN "act_task"."assignee" IS '任务分配人';
COMMENT ON COLUMN "act_task"."assigned" IS '是否已分配';
COMMENT ON COLUMN "act_task"."candidate_groups" IS '候选组';
COMMENT ON COLUMN "act_task"."candidate_users" IS '候选用户';
COMMENT ON COLUMN "act_task"."retries" IS '重试次数';
COMMENT ON COLUMN "act_task"."worker" IS '执行者';
COMMENT ON COLUMN "act_task"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "act_task"."process_definition_version" IS '流程定义版本';
COMMENT ON COLUMN "act_task"."bpmn_process_id" IS 'BPMN 流程 ID';
COMMENT ON COLUMN "act_task"."flow_node_bpmn_id" IS 'BPMN 流程节点 ID';
COMMENT ON COLUMN "act_task"."flow_node_instance_id" IS 'BPMN 流程节点实例 ID';
COMMENT ON COLUMN "act_task"."form_key" IS '表单key';
COMMENT ON COLUMN "act_task"."form_id" IS '表单id';
COMMENT ON COLUMN "act_task"."form_version" IS '表单版本';
COMMENT ON COLUMN "act_task"."is_form_embedded" IS '是否内置表单';
COMMENT ON COLUMN "act_task"."follow_up_date" IS '跟进日期';
COMMENT ON COLUMN "act_task"."implementation" IS '任务实现 任务的具体实现逻辑，通常是代码或脚本';
COMMENT ON COLUMN "act_task"."position" IS '偏移量';
COMMENT ON COLUMN "act_task"."partition_id" IS '分区ID';
COMMENT ON COLUMN "act_task"."create_by" IS '创建人';
COMMENT ON COLUMN "act_task"."update_by" IS '更新人';
COMMENT ON COLUMN "act_task"."create_time" IS '创建时间';
COMMENT ON COLUMN "act_task"."update_time" IS '更新时间';
COMMENT ON COLUMN "act_task"."tenant_id" IS '租户Id';

-- ----------------------------
-- Table structure for act_task_variable
-- ----------------------------
DROP TABLE IF EXISTS "act_task_variable";
CREATE TABLE "act_task_variable" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "key" int8,
  "task_id" varchar(255) COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "value" text COLLATE "pg_catalog"."default",
  "full_value" text COLLATE "pg_catalog"."default",
  "is_preview" bool,
  "partition_id" int4,
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for act_timer
-- ----------------------------
DROP TABLE IF EXISTS "act_timer";
CREATE TABLE "act_timer" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "time_key" int8 NOT NULL,
  "position" int8,
  "due_date" int8,
  "repetitions" int4,
  "target_element_id" varchar(255) COLLATE "pg_catalog"."default",
  "process_instance_key" int8,
  "element_instance_key" int8,
  "process_definition_key" int8,
  "state" varchar(50) COLLATE "pg_catalog"."default" DEFAULT 'CREATED'::character varying,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "act_timer"."id" IS '主键ID';
COMMENT ON COLUMN "act_timer"."time_key" IS '定时器唯一标识';
COMMENT ON COLUMN "act_timer"."position" IS '定时器位置';
COMMENT ON COLUMN "act_timer"."due_date" IS '到期时间';
COMMENT ON COLUMN "act_timer"."repetitions" IS '重复次数';
COMMENT ON COLUMN "act_timer"."target_element_id" IS '目标元素ID';
COMMENT ON COLUMN "act_timer"."process_instance_key" IS '流程实例Key';
COMMENT ON COLUMN "act_timer"."element_instance_key" IS '元素实例Key';
COMMENT ON COLUMN "act_timer"."process_definition_key" IS '流程定义Key';
COMMENT ON COLUMN "act_timer"."state" IS '定时器状态';
COMMENT ON COLUMN "act_timer"."create_by" IS '创建用户ID';
COMMENT ON COLUMN "act_timer"."update_by" IS '更新用户ID';
COMMENT ON COLUMN "act_timer"."create_time" IS '创建时间';
COMMENT ON COLUMN "act_timer"."update_time" IS '更新时间';
COMMENT ON COLUMN "act_timer"."tenant_id" IS '租户ID';

-- ----------------------------
-- Table structure for act_user_task
-- ----------------------------
DROP TABLE IF EXISTS "act_user_task";
CREATE TABLE "act_user_task" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "key" int8 NOT NULL,
  "position" int8,
  "process_instance_key" int8,
  "process_definition_key" int8,
  "element_instance_key" int8,
  "assignee" varchar(255) COLLATE "pg_catalog"."default",
  "candidate_groups" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "form_key" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "state" varchar(50) COLLATE "pg_catalog"."default" DEFAULT 'CREATED'::character varying,
  "timestamp" int8,
  "start_time" int8,
  "end_time" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "update_by" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;

-- ----------------------------
-- Table structure for act_variable
-- ----------------------------
DROP TABLE IF EXISTS "act_variable";
CREATE TABLE "act_variable" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "key" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "value" text COLLATE "pg_catalog"."default" NOT NULL,
  "full_value" text COLLATE "pg_catalog"."default",
  "scope_flow_node_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "process_instance_key" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "process_definition_key" int8 NOT NULL,
  "scope_key" int8 NOT NULL,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "position" int8,
  "is_preview" bool,
  "timestamp" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "act_variable"."id" IS '主键id';
COMMENT ON COLUMN "act_variable"."key" IS 'zeebe原始key';
COMMENT ON COLUMN "act_variable"."name" IS '变量名';
COMMENT ON COLUMN "act_variable"."value" IS '变量值';
COMMENT ON COLUMN "act_variable"."full_value" IS '变量的完整值，通常用于存储较长的数据';
COMMENT ON COLUMN "act_variable"."scope_flow_node_id" IS '变量作用域对应的流程节点 ID';
COMMENT ON COLUMN "act_variable"."process_instance_key" IS '流程实例key';
COMMENT ON COLUMN "act_variable"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "act_variable"."scope_key" IS '变量的作用域的唯一标识符';
COMMENT ON COLUMN "act_variable"."bpmn_process_id" IS 'BPMN 流程 ID';
COMMENT ON COLUMN "act_variable"."position" IS '偏移量';
COMMENT ON COLUMN "act_variable"."is_preview" IS '标识变量是否为预览状态';
COMMENT ON COLUMN "act_variable"."timestamp" IS '变量的时间戳';
COMMENT ON COLUMN "act_variable"."create_by" IS '创建人';
COMMENT ON COLUMN "act_variable"."update_by" IS '更新人';
COMMENT ON COLUMN "act_variable"."create_time" IS '创建时间';
COMMENT ON COLUMN "act_variable"."update_time" IS '更新时间';
COMMENT ON COLUMN "act_variable"."tenant_id" IS '租户';

-- ----------------------------
-- Table structure for act_variable_update
-- ----------------------------
DROP TABLE IF EXISTS "act_variable_update";
CREATE TABLE "act_variable_update" (
  "partition_id_with_position" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "variable_key" int8,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "value" text COLLATE "pg_catalog"."default",
  "process_instance_key" int8,
  "scope_key" int8,
  "timestamp" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for app_auth
-- ----------------------------
DROP TABLE IF EXISTS "app_auth";
CREATE TABLE "app_auth" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "project_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "project_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "salt" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "access_key" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "secret_key" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "expire_time" timestamp(6) NOT NULL,
  "status" int2 NOT NULL,
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "update_by" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "update_time" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "app_auth"."id" IS '主键';
COMMENT ON COLUMN "app_auth"."project_id" IS '应用id';
COMMENT ON COLUMN "app_auth"."project_name" IS '项目立项名称';
COMMENT ON COLUMN "app_auth"."salt" IS '盐值';
COMMENT ON COLUMN "app_auth"."access_key" IS '访问秘钥';
COMMENT ON COLUMN "app_auth"."secret_key" IS '秘钥';
COMMENT ON COLUMN "app_auth"."expire_time" IS '秘钥到期时间';
COMMENT ON COLUMN "app_auth"."status" IS '状态，#0:已禁用，#1:启用';
COMMENT ON COLUMN "app_auth"."tenant_id" IS '租户id';
COMMENT ON COLUMN "app_auth"."create_by" IS '创建人';
COMMENT ON COLUMN "app_auth"."update_by" IS '修改人';
COMMENT ON COLUMN "app_auth"."create_time" IS '创建时间';
COMMENT ON COLUMN "app_auth"."update_time" IS '修改时间';
COMMENT ON TABLE "app_auth" IS '应用授权管理表';

-- ----------------------------
-- Table structure for cpit_element_template
-- ----------------------------
DROP TABLE IF EXISTS "cpit_element_template";
CREATE TABLE "cpit_element_template" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "template_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "json" text COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "update_by" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "update_time" timestamp(6) NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for cpit_execution_flow_instance
-- ----------------------------
DROP TABLE IF EXISTS "cpit_execution_flow_instance";
CREATE TABLE "cpit_execution_flow_instance" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "year" int4 NOT NULL,
  "month" int2 NOT NULL,
  "date" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "flow_node_instance_id" int8 NOT NULL,
  "flow_node_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "flow_node_name" varchar(255) COLLATE "pg_catalog"."default",
  "input_variable" text COLLATE "pg_catalog"."default",
  "output_variable" text COLLATE "pg_catalog"."default",
  "parent_flow_node_id" varchar(255) COLLATE "pg_catalog"."default",
  "process_instance_key" int8 NOT NULL,
  "process_definition_key" int8 NOT NULL,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar(50) COLLATE "pg_catalog"."default",
  "start_date" int8,
  "end_date" int8,
  "duration" int8,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "cpit_execution_flow_instance"."year" IS '年';
COMMENT ON COLUMN "cpit_execution_flow_instance"."month" IS '月份';
COMMENT ON COLUMN "cpit_execution_flow_instance"."date" IS '日期，格式为 yyyy-MM-dd';
COMMENT ON COLUMN "cpit_execution_flow_instance"."flow_node_instance_id" IS '流程实例节点id';
COMMENT ON COLUMN "cpit_execution_flow_instance"."flow_node_id" IS '否	流程元素的唯一标识符';
COMMENT ON COLUMN "cpit_execution_flow_instance"."flow_node_name" IS '否	流程元素的名称';
COMMENT ON COLUMN "cpit_execution_flow_instance"."input_variable" IS '输入变量';
COMMENT ON COLUMN "cpit_execution_flow_instance"."output_variable" IS '输出变量';
COMMENT ON COLUMN "cpit_execution_flow_instance"."parent_flow_node_id" IS '是	当前节点的父节点的唯一标识符';
COMMENT ON COLUMN "cpit_execution_flow_instance"."process_instance_key" IS '流程实例key';
COMMENT ON COLUMN "cpit_execution_flow_instance"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "cpit_execution_flow_instance"."bpmn_process_id" IS 'BPMN流程ID';
COMMENT ON COLUMN "cpit_execution_flow_instance"."state" IS '节点状态';
COMMENT ON COLUMN "cpit_execution_flow_instance"."type" IS '事件类型';
COMMENT ON COLUMN "cpit_execution_flow_instance"."start_date" IS '开始时间';
COMMENT ON COLUMN "cpit_execution_flow_instance"."end_date" IS '结束时间';
COMMENT ON COLUMN "cpit_execution_flow_instance"."duration" IS '耗时，单位通常为毫秒';
COMMENT ON COLUMN "cpit_execution_flow_instance"."create_by" IS '创建人';
COMMENT ON COLUMN "cpit_execution_flow_instance"."update_by" IS '更新人';
COMMENT ON COLUMN "cpit_execution_flow_instance"."create_time" IS '创建时间';
COMMENT ON COLUMN "cpit_execution_flow_instance"."update_time" IS '更新时间';
COMMENT ON COLUMN "cpit_execution_flow_instance"."tenant_id" IS '租户';

-- ----------------------------
-- Table structure for cpit_process_design
-- ----------------------------
DROP TABLE IF EXISTS "cpit_process_design";
CREATE TABLE "cpit_process_design" (
  "id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "business_type" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "process_xml" text COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "process_status" int2 NOT NULL,
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "process_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "process_key" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar(64) COLLATE "pg_catalog"."default",
  "is_edited" bool,
  "process_yml" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "cpit_process_design"."id" IS 'id';
COMMENT ON COLUMN "cpit_process_design"."business_type" IS '业务类型';
COMMENT ON COLUMN "cpit_process_design"."process_xml" IS '流程设计xml';
COMMENT ON COLUMN "cpit_process_design"."tenant_id" IS '租户id';
COMMENT ON COLUMN "cpit_process_design"."process_status" IS '流程设计状态';
COMMENT ON COLUMN "cpit_process_design"."create_by" IS '创建人';
COMMENT ON COLUMN "cpit_process_design"."update_by" IS '更新人';
COMMENT ON COLUMN "cpit_process_design"."create_time" IS '创建时间';
COMMENT ON COLUMN "cpit_process_design"."update_time" IS '更新时间';
COMMENT ON COLUMN "cpit_process_design"."process_name" IS '定义名';
COMMENT ON COLUMN "cpit_process_design"."process_key" IS 'bpmn流程id';
COMMENT ON COLUMN "cpit_process_design"."type" IS '类型';
COMMENT ON COLUMN "cpit_process_design"."is_edited" IS '是否编辑过';
COMMENT ON COLUMN "cpit_process_design"."process_yml" IS '流程设计yml';
COMMENT ON TABLE "cpit_process_design" IS '流程设计表';

-- ----------------------------
-- Table structure for cpit_statistics_execution
-- ----------------------------
DROP TABLE IF EXISTS "cpit_statistics_execution";
CREATE TABLE "cpit_statistics_execution" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "year" int4 NOT NULL,
  "month" int2 NOT NULL,
  "date" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "bpmn_process_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "process_definition_key" int8 NOT NULL,
  "complete_count" int8,
  "active_count" int8,
  "failed_count" int8,
  "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "tenant_id" varchar(64) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "cpit_statistics_execution"."id" IS '主键id';
COMMENT ON COLUMN "cpit_statistics_execution"."year" IS '年';
COMMENT ON COLUMN "cpit_statistics_execution"."month" IS '月份';
COMMENT ON COLUMN "cpit_statistics_execution"."date" IS '日期';
COMMENT ON COLUMN "cpit_statistics_execution"."bpmn_process_id" IS '流程设计id';
COMMENT ON COLUMN "cpit_statistics_execution"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "cpit_statistics_execution"."complete_count" IS '完成数量';
COMMENT ON COLUMN "cpit_statistics_execution"."active_count" IS '激活数量';
COMMENT ON COLUMN "cpit_statistics_execution"."failed_count" IS '失败数量';
COMMENT ON COLUMN "cpit_statistics_execution"."create_time" IS '开始时间';
COMMENT ON COLUMN "cpit_statistics_execution"."tenant_id" IS '租户';

-- ----------------------------
-- Table structure for raw_record
-- ----------------------------
DROP TABLE IF EXISTS "raw_record";
CREATE TABLE "raw_record" (
  "id" int8 NOT NULL,
  "process_definition_key" int8,
  "process_instance_key" int8,
  "partition_id" int4 NOT NULL,
  "intent" varchar(255) COLLATE "pg_catalog"."default",
  "record_type" varchar(255) COLLATE "pg_catalog"."default",
  "rejection_type" varchar(255) COLLATE "pg_catalog"."default",
  "rejection_reason" text COLLATE "pg_catalog"."default",
  "broker_version" varchar(255) COLLATE "pg_catalog"."default",
  "value_type" varchar(255) COLLATE "pg_catalog"."default",
  "key" int8 NOT NULL,
  "position" int8 NOT NULL,
  "timestamp" int8 NOT NULL,
  "source_record_position" int8 NOT NULL,
  "record_version" int4 NOT NULL,
  "value_json" text COLLATE "pg_catalog"."default",
  "authorizations_json" text COLLATE "pg_catalog"."default",
  "operation_reference" int8 NOT NULL,
  "create_by" varchar(255) COLLATE "pg_catalog"."default",
  "update_by" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "tenant_id" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "raw_record"."id" IS 'id';
COMMENT ON COLUMN "raw_record"."process_definition_key" IS '流程定义key';
COMMENT ON COLUMN "raw_record"."process_instance_key" IS '流程实例key';
COMMENT ON COLUMN "raw_record"."partition_id" IS '分区id';
COMMENT ON COLUMN "raw_record"."intent" IS 'intent';
COMMENT ON COLUMN "raw_record"."record_type" IS '记录类型';
COMMENT ON COLUMN "raw_record"."rejection_type" IS '驳回版本';
COMMENT ON COLUMN "raw_record"."rejection_reason" IS '驳回原因';
COMMENT ON COLUMN "raw_record"."broker_version" IS 'broker版本';
COMMENT ON COLUMN "raw_record"."value_type" IS '数据值类型';
COMMENT ON COLUMN "raw_record"."key" IS 'key';
COMMENT ON COLUMN "raw_record"."position" IS '偏移位';
COMMENT ON COLUMN "raw_record"."timestamp" IS '时间戳';
COMMENT ON COLUMN "raw_record"."source_record_position" IS '原数据偏移位';
COMMENT ON COLUMN "raw_record"."record_version" IS '记录版本';
COMMENT ON COLUMN "raw_record"."value_json" IS '数据';
COMMENT ON COLUMN "raw_record"."create_by" IS '创建人';
COMMENT ON COLUMN "raw_record"."update_by" IS '更新人';
COMMENT ON COLUMN "raw_record"."create_time" IS '创建时间';
COMMENT ON COLUMN "raw_record"."update_time" IS '更新时间';
COMMENT ON COLUMN "raw_record"."tenant_id" IS '租户id';
COMMENT ON TABLE "raw_record" IS '引擎原数据表';

-- ----------------------------
-- Primary Key structure for table act_decision
-- ----------------------------
ALTER TABLE "act_decision" ADD CONSTRAINT "decision_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_decision_evaluation
-- ----------------------------
ALTER TABLE "act_decision_evaluation" ADD CONSTRAINT "decision_evaluation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_decision_evaluation_input
-- ----------------------------
ALTER TABLE "act_decision_evaluation_input" ADD CONSTRAINT "decision_evaluation_input_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_decision_evaluation_output
-- ----------------------------
ALTER TABLE "act_decision_evaluation_output" ADD CONSTRAINT "decision_evaluation_output_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_decision_requirements
-- ----------------------------
ALTER TABLE "act_decision_requirements" ADD CONSTRAINT "decision_requirements_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_element_instance_state_transition
-- ----------------------------
ALTER TABLE "act_element_instance_state_transition" ADD CONSTRAINT "element_instance_state_transition_pkey" PRIMARY KEY ("partition_id_with_position");

-- ----------------------------
-- Primary Key structure for table act_error
-- ----------------------------
ALTER TABLE "act_error" ADD CONSTRAINT "error_pkey" PRIMARY KEY ("position");

-- ----------------------------
-- Primary Key structure for table act_event
-- ----------------------------
ALTER TABLE "act_event" ADD CONSTRAINT "event_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_event_meta_data
-- ----------------------------
ALTER TABLE "act_event_meta_data" ADD CONSTRAINT "event_meta_data_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table act_flow_node_instance
-- ----------------------------
CREATE INDEX "idx_cursor_query_new" ON "act_flow_node_instance" USING btree (
  "processed" "pg_catalog"."bool_ops" ASC NULLS LAST,
  "partition_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "position" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_flow_node_instance
-- ----------------------------
ALTER TABLE "act_flow_node_instance" ADD CONSTRAINT "flow_node_instance_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_form
-- ----------------------------
ALTER TABLE "act_form" ADD CONSTRAINT "form_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_incident
-- ----------------------------
ALTER TABLE "act_incident" ADD CONSTRAINT "act_incident_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_message
-- ----------------------------
ALTER TABLE "act_message" ADD CONSTRAINT "message_pkey" PRIMARY KEY ("key");

-- ----------------------------
-- Primary Key structure for table act_message_correlation
-- ----------------------------
ALTER TABLE "act_message_correlation" ADD CONSTRAINT "message_correlation_pkey" PRIMARY KEY ("partition_id_with_position");

-- ----------------------------
-- Primary Key structure for table act_message_subscription
-- ----------------------------
ALTER TABLE "act_message_subscription" ADD CONSTRAINT "message_subscription_pkey" PRIMARY KEY ("key_");

-- ----------------------------
-- Primary Key structure for table act_message_variable
-- ----------------------------
ALTER TABLE "act_message_variable" ADD CONSTRAINT "message_variable_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_process
-- ----------------------------
ALTER TABLE "act_process" ADD CONSTRAINT "process_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table act_process_flow_node
-- ----------------------------
ALTER TABLE "act_process_flow_node" ADD CONSTRAINT "uk_process_flow_node" UNIQUE ("process_definition_key", "flow_node_id");

-- ----------------------------
-- Primary Key structure for table act_process_flow_node
-- ----------------------------
ALTER TABLE "act_process_flow_node" ADD CONSTRAINT "process_flow_node_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table act_process_instance
-- ----------------------------
CREATE UNIQUE INDEX "process_instance_pkey" ON "act_process_instance" USING btree (
  "id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_sequence_flow
-- ----------------------------
ALTER TABLE "act_sequence_flow" ADD CONSTRAINT "act_sequence_flow_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_signal
-- ----------------------------
ALTER TABLE "act_signal" ADD CONSTRAINT "signal_pkey" PRIMARY KEY ("key_");

-- ----------------------------
-- Primary Key structure for table act_signal_subscription
-- ----------------------------
ALTER TABLE "act_signal_subscription" ADD CONSTRAINT "signal_subscription_pkey" PRIMARY KEY ("key_");

-- ----------------------------
-- Primary Key structure for table act_signal_variable
-- ----------------------------
ALTER TABLE "act_signal_variable" ADD CONSTRAINT "signal_variable_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_task
-- ----------------------------
ALTER TABLE "act_task" ADD CONSTRAINT "task_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_task_variable
-- ----------------------------
ALTER TABLE "act_task_variable" ADD CONSTRAINT "task_variable_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_timer
-- ----------------------------
ALTER TABLE "act_timer" ADD CONSTRAINT "timer_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_user_task
-- ----------------------------
ALTER TABLE "act_user_task" ADD CONSTRAINT "user_task_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table act_variable
-- ----------------------------
CREATE INDEX "idx_variable_scope" ON "act_variable" USING btree (
  "scope_flow_node_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "timestamp" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_variable
-- ----------------------------
ALTER TABLE "act_variable" ADD CONSTRAINT "variable_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table act_variable_update
-- ----------------------------
ALTER TABLE "act_variable_update" ADD CONSTRAINT "variable_update_pkey" PRIMARY KEY ("partition_id_with_position");

-- ----------------------------
-- Primary Key structure for table app_auth
-- ----------------------------
ALTER TABLE "app_auth" ADD CONSTRAINT "app_auth_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table cpit_element_template
-- ----------------------------
CREATE UNIQUE INDEX "template_id_index" ON "cpit_element_template" USING btree (
  "template_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table cpit_element_template
-- ----------------------------
ALTER TABLE "cpit_element_template" ADD CONSTRAINT "cpit_process_design_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table cpit_execution_flow_instance
-- ----------------------------
CREATE INDEX "idx_execution_flow_instance" ON "cpit_execution_flow_instance" USING btree (
  "flow_node_instance_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
  "state" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table cpit_execution_flow_instance
-- ----------------------------
ALTER TABLE "cpit_execution_flow_instance" ADD CONSTRAINT "execution_instance_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table cpit_process_design
-- ----------------------------
ALTER TABLE "cpit_process_design" ADD CONSTRAINT "cpit_process_design_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table cpit_statistics_execution
-- ----------------------------
CREATE UNIQUE INDEX "idx_date_process_definition_key" ON "cpit_statistics_execution" USING btree (
  "date" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "process_definition_key" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table cpit_statistics_execution
-- ----------------------------
ALTER TABLE "cpit_statistics_execution" ADD CONSTRAINT "cpit_statistics_process_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table raw_record
-- ----------------------------
ALTER TABLE "raw_record" ADD CONSTRAINT "raw_record_pkey" PRIMARY KEY ("id");
