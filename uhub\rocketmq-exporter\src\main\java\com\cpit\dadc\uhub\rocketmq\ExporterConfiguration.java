package com.cpit.dadc.uhub.rocketmq;


import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;

import java.util.Optional;

public class ExporterConfiguration {

    private final String namesrvAddr = "59.110.94.128:9876";
    private final String namespace = "uhub";
    private final String producerGroup = "uhub";
    private final String userName = "guest";
    private final String password = "guest";

    private final String accessKey = "admin";
    private final String secretKey = "123456";

    private final Long delay = 5L;
    private static final String ENV_PREFIX = "ZEEBE_ROCKETMQ_";

    private String enabledValueTypes = "";

    private String enabledRecordTypes = "";

    public String getNamesrvAddr() {
        return getEnv("NAMESRVADDR").orElse(namesrvAddr);
    }

    public String getNamespace() {
        return getEnv("NAMESPACE").orElse(namespace);
    }

    public String getProducerGroup() {
        return getEnv("PRODUCERGROUP").orElse(producerGroup);
    }

    public String getUsername() {return getEnv("USERNAME").orElse(userName);}

    public String getPassword() {return getEnv("PASSWORD").orElse(password);}

    public String getAccessKey() {return getEnv("ACCESSKEY").orElse(accessKey);}

    public String getSecretKey() {return getEnv("SECRETKEY").orElse(secretKey);}

    public Long getDelay() {
        return getEnv("DELAY").map(Long::parseLong).orElse(delay);
    }


    public String getEnabledValueTypes() {
        return getEnv("ENABLED_VALUE_TYPES").orElse(enabledValueTypes);
    }

    public String getEnabledRecordTypes() {
        return getEnv("ENABLED_RECORD_TYPES").orElse(enabledRecordTypes);
    }

    private Optional<String> getEnv(String name) {
        return Optional.ofNullable(System.getenv(ENV_PREFIX + name));
    }

/*    public DefaultMQProducer createProducer() {
        producer = new DefaultMQProducer(getNamespace(), getProducerGroup());
        producer.setNamesrvAddr(getHost() + ":" + getPort());
        try {
            producer.start();
        } catch (MQClientException e) {
            throw new RuntimeException(e);
        }
        return producer;
    }

    public void close() {
        producer.shutdown();
    }*/


}