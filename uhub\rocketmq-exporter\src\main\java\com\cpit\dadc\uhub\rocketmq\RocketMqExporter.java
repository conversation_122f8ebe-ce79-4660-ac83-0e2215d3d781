package com.cpit.dadc.uhub.rocketmq;

import com.alibaba.fastjson.JSONObject;
import io.camunda.zeebe.exporter.api.Exporter;
import io.camunda.zeebe.exporter.api.context.Context;
import io.camunda.zeebe.exporter.api.context.Controller;
import io.camunda.zeebe.protocol.record.Record;
import io.camunda.zeebe.protocol.record.RecordValue;
import io.camunda.zeebe.protocol.record.ValueType;
import io.camunda.zeebe.protocol.record.value.JobBatchRecordValue;
import io.camunda.zeebe.protocol.record.value.TenantOwned;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.slf4j.Logger;

import java.util.List;


public class RocketMqExporter implements Exporter {

    private Logger logger;

    private Controller controller;

    private DefaultMQProducer producer;

    private ExporterConfiguration configuration;



    @Override
    public void configure(Context context) throws Exception {
        logger = context.getLogger();
        configuration = context.getConfiguration().instantiate(ExporterConfiguration.class);

        logger.info("Exporter configured with {}", JSONObject.toJSONString(configuration));

        final var filter = new RecordFilter(configuration);
        context.setFilter(filter);
    }

    @Override
    public void open(Controller controller) {
        this.controller = controller;
        producer = createProducer();
        logger.info("Exporter opened");
    }


    @Override
    public void close() {
        producer.shutdown();
    }

    @Override
    public void export(Record<?> record) {
        // 根据配置过滤 JOB_BATCH 和 COMMAND_DISTRIBUTION 记录
        if (record.getValueType() == ValueType.JOB_BATCH || record.getValueType() == ValueType.COMMAND_DISTRIBUTION) {
            logger.info("Skipping export for {} record with key: {}", record.getValueType().name(), record.getKey());
            return;
        }
        Long key = record.getKey();
        String valueType = record.getValueType().name();
        RecordValue value = record.getValue();
        logger.info(
                "\n" +
                        "============================================================\n" +
                        ">>> Exporter Received Record:\n" +
                        ">>> - Value Type: {}\n" +
                        ">>> - Key:       {}\n" +
                        ">>> - Value:     {}\n" +
                        "============================================================",
                valueType, key, value.toJson()
        );
        String tenantId = getString(record);

        if (tenantId == null || TenantOwned.DEFAULT_TENANT_IDENTIFIER.equals(tenantId)) {
            logger.info("AK、SK配置有误，未找到对应的租户");
            return;
        }

        if (producer != null) {
            Message message = new Message(tenantId + "_" + record.getValueType().name(), "tag", record.toJson().getBytes());
            try {
                producer.send(message);
            } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        controller.updateLastExportedRecordPosition(record.getPosition());
    }

    private static String getString(Record<?> record) {
        String tenantId = null;

        if (record.getValue() instanceof TenantOwned) {
            tenantId = ((TenantOwned) record.getValue()).getTenantId();
        } else if (record.getValue() instanceof JobBatchRecordValue) {
            // 对于 JobBatchRecordValue 类型，处理方式会有所不同
            List<String> tenantIds = ((JobBatchRecordValue) record.getValue()).getTenantIds();
            if (tenantIds != null && !tenantIds.isEmpty()) {
                tenantId = tenantIds.get(0);  // 你可以根据需求选择如何处理多个 tenantId
            }
        }
        return tenantId;
    }


    protected DefaultMQProducer createProducer() {
        RocketMQProperties rocketMQProperties = new RocketMQProperties();
        rocketMQProperties.setNameServer(configuration.getNamesrvAddr());

        RocketMQProperties.Producer producer =  new RocketMQProperties.Producer();

        producer.setNamespace(configuration.getNamespace());
        producer.setAccessKey(configuration.getAccessKey());
        producer.setSecretKey(configuration.getSecretKey());
        producer.setGroup(configuration.getProducerGroup());
        producer.setSendMessageTimeout(100000);
        rocketMQProperties.setProducer(producer);
        logger.info("producer: " + producer);
        DefaultMQProducer defaultMQProducer = RocketMQUtil.defaultMQProducer(rocketMQProperties);

        try {
            defaultMQProducer.start();
        } catch (MQClientException e) {
            throw new RuntimeException(e);
        }
        return defaultMQProducer;
    }



}
