package com.cpit.dadc.uhub.common.config;

import jakarta.annotation.PreDestroy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
public class ThreadPoolConfig {

    @Bean(name = "threadPoolTaskExecutor")
    public ThreadPoolTaskExecutor messageHandlerThreadPool() {
        // 获取 CPU 核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数：CPU 核心数 * 2（假设是 I/O 密集型任务）
        executor.setCorePoolSize(cpuCores * 2);
        // 最大线程数：CPU 核心数 * 4
        executor.setMaxPoolSize(cpuCores * 4);
        // 队列容量：根据需求设置
        executor.setQueueCapacity(1000);
        // 线程空闲时间：60 秒
        executor.setKeepAliveSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix("MessageHandler-");
        // 拒绝策略：由调用线程处理任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        executor.initialize();
        return executor;
    }

    // 将线程池存储为类成员变量
    private ExecutorService notificationListenerThreadPool;

    @Bean(name = "notificationListenerThreadPool")
    public ExecutorService createNotificationListenerThreadPool() {
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // 对于IO密集型任务，核心线程数可以设置为CPU核心数的1-2倍。
        int corePoolSize = cpuCores * 2;
        int maxPoolSize = cpuCores * 4;  // 允许更多线程来处理突发负载

        // 使用无界队列或根据业务需求调整队列大小
        BlockingQueue<Runnable> workQueue = new SynchronousQueue<>();

        // 设置较短的空闲线程存活时间以快速释放资源
        long keepAliveTime = 30L;
        TimeUnit timeUnit = TimeUnit.SECONDS;

        // 自定义线程工厂和拒绝策略
        ThreadFactory threadFactory = new CustomThreadFactory("notification-pool-", false);
        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();

        // 创建线程池并赋值给类成员变量
        this.notificationListenerThreadPool = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveTime,
                timeUnit,
                workQueue,
                threadFactory,
                handler
        );

        return this.notificationListenerThreadPool;
    }

    // 自定义线程工厂，不依赖 SecurityManager
    static class CustomThreadFactory implements ThreadFactory {
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        private final boolean daemon;

        CustomThreadFactory(String prefix, boolean daemon) {
            this.group = Thread.currentThread().getThreadGroup();
            this.namePrefix = prefix + "thread-";
            this.daemon = daemon;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + threadNumber.getAndIncrement(), 0);
            t.setDaemon(daemon);  // 根据构造函数参数设置守护线程属性
            if (t.getPriority() != Thread.NORM_PRIORITY) t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }
    }

    // 如果需要自定义拒绝策略，可以扩展此部分
    // 此处使用了默认的CallerRunsPolicy，它会让调用线程执行被拒绝的任务

    @PreDestroy
    public void destroy() {
        if (this.notificationListenerThreadPool != null && !this.notificationListenerThreadPool.isShutdown()) {
            this.notificationListenerThreadPool.shutdown();
        }
    }
}