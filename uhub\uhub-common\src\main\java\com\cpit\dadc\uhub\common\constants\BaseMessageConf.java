package com.cpit.dadc.uhub.common.constants;

/**
 * 消息通知
 *
 * @author: Han.RX
 * @create: 2022-03-18-17:16
 */
public class BaseMessageConf {
    public static final String TRUE = "true";
    public static final String FALSE = "false";
    public static final String OPERATION_SUCCESS = "操作成功";
    public static final String OPERATION_FAIL = "操作失败";
    public static final String INSERT_SUCCESS = "插入成功";
    public static final String INSERT_FAIL = "插入失败";
    public static final String UPDATE_SUCCESS = "更新成功";
    public static final String UPDATE_FAIL = "更新失败";
    public static final String DELETE_SUCCESS = "删除成功";
    public static final String BATCH_DELETE_SUCCESS = "批量删除成功";
    public static final String DELETE_FAIL = "删除失败";
    public static final String ENTITY_NOT_EXIST = "该实体不存在";
    public static final String SYSTEM_CONFIG_NOT_EXIST = "系统配置有误";
    public static final String KEYWORD_IS_NOT_EMPTY = "关键字不能为空";
    public static final String LOGIN_DISABLE = "该账号已被封禁";
    public static final String NO_ROLE = "没有分配角色权限";
    public static final String INTERFACE_FREQUENTLY = "接口过于频繁调用";
    public static final String PARAM_INCORRECT = "传入参数有误！";
    public static final String CHILDREN_MENU_UNDER_THIS_MENU = "该菜单下还有子菜单！";
    public final static String ERROR_PASSWORD = "密码错误";
    public static final String DATA_NO_PRIVILEGE = "该数据无权限访问";
    public static final String RESTAPI_NO_PRIVILEGE = "您无权进行该操作";
    public static final String ACCESS_NO_PRIVILEGE = "该资源无权限访问";
    public static final String LOGIN_TIMEOUT = "您已退出，请重新登录";


    //=========================================
    // 查询相关异常码
    //=========================================

    /**
     * 查询操作默认异常码
     */
    public static final String QUERY_DEFAULT_ERROR = "查询操作失败";

    //=========================================
    // 新增操作相关异常码
    //=========================================

    /**
     * 插入操作默认异常码
     */
    public static final String INSERT_DEFAULT_ERROR = "插入操作失败";


    //=========================================
    // 更新操作相关异常码
    //=========================================

    /**
     * 更新操作默认异常码
     */
    public static final String UPDATE_DEFAULT_ERROR = "更新操作失败";


    //=========================================
    // 删除操作相关异常码
    //=========================================

    /**
     * 删除操作默认异常码
     */
    public static final String DELETE_DEFAULT_ERROR = "删除操作失败";
    public static final String DELETE_FAILED_PLEASE_CHECK_UID = "删除操作失败，请检查uid是否合法";
}
