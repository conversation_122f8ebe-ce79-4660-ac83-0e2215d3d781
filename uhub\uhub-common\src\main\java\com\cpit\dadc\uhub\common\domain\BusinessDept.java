package com.cpit.dadc.uhub.common.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 BussinessDept
 *
 * @date 20220402
 * <AUTHOR>
 */
@Data
public class BusinessDept implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    private String deptCode;
    private String deptName;
    /** 部门唯一标识 */
    private String parentId;
    /** 子部门 */
    private List<BusinessDept> children = new ArrayList<BusinessDept>();
}
