package com.cpit.dadc.uhub.common.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BusinessDict implements Serializable  {

    private static final long serialVersionUID = 1L;

    private String id;
    private String val;
    private String code;
    private String name;
    private String parentId;
    private Integer leaf;

}
