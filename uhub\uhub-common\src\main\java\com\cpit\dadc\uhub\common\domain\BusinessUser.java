package com.cpit.dadc.uhub.common.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户对象 bussinessUser
 * @date 20220402
 * <AUTHOR>
 */
@Data
public class BusinessUser implements Serializable {
    private static final long serialVersionUID = 1L;

    private String userId;
    private String userName;
    private String nickName;
    private String avatar;
    // 0=男，1=女，2=未知
    private  String sex;
    private String phoneNumber;
    private String email;
    // 部门
    private BusinessDept dept;
    // 分管部门
    private List<BusinessDept> deptCharge;
    // roles
    private List<BusinessRole> roles;
    // posts
    private List<BusinessPost> posts;

    public List<String> tenantIds;
}
