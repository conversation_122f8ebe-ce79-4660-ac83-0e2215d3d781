package com.cpit.dadc.uhub.common.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户对象 OperatorUser
 * @date 2024-9-26
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperatorUser implements Serializable {
    private static final long serialVersionUID = 1L;

    private String userId;
    private String userName;
    private String nickName;
    private String phoneNumber;
    private String email;
    private BusinessDept dept;

    // 构造函数，接受 BusinessUser 对象并赋值
    public OperatorUser(BusinessUser businessUser) {
        this.userId = businessUser.getUserId();
        this.userName = businessUser.getUserName();
        this.nickName = businessUser.getNickName();
        this.phoneNumber = businessUser.getPhoneNumber();
        this.email = businessUser.getEmail();
        this.dept = businessUser.getDept();
    }
}
