package com.cpit.dadc.uhub.common.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 页码
 *
 * @author: wyh
 * @date: 2022-06-09
 */
@Data
public class PageDTO implements Serializable {
    @Schema(description = "第几页，默认1")
    private Integer pageNum = 1;

    @Schema(description = "每页条数，默认10")
    private Integer pageSize = 10;
}
