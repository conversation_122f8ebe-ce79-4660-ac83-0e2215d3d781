package com.cpit.dadc.uhub.common.domain.dto.auth;

import com.cpit.dadc.uhub.common.valid.group.BaseGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description:
 * @date: 2024-09-06
 **/
@Data
public class AppAuthDTO implements Serializable {
    @Schema(description = "id")
    @Null(message = "id需为空", groups = {BaseGroup.Insert.class})
    @NotBlank(message = "id不能为空", groups = {BaseGroup.Update.class})
    private String id;

    @Schema(description = "租户id", required = true)
    @NotBlank(message = "租户id不能为空")
    private String tenantId;

    @Schema(description = "项目id", required = true)
    @NotBlank(message = "项目不能为空")
    private String projectId;

    @Schema(description = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    @Schema(description = "秘钥到期时间", required = true)
    @NotNull(message = "秘钥到期时间不能为空")
    private LocalDateTime expireTime;

    @Schema(description = "状态，#0:已禁用，#1:启", required = true)
    @NotBlank(message = "状态不能为空")
    private Integer status;
}
