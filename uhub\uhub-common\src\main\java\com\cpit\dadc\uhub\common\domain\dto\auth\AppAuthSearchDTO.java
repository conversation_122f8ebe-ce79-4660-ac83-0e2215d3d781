package com.cpit.dadc.uhub.common.domain.dto.auth;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-09-06
 **/
@Data
public class AppAuthSearchDTO extends PageDTO implements Serializable {
    @Schema(description = "记录id")
    private String id;

    @Schema(description = "应用名")
    private String projectName;
}
