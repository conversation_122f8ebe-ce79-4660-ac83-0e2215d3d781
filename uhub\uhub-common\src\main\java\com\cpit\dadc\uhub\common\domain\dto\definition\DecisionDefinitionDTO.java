package com.cpit.dadc.uhub.common.domain.dto.definition;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/11 15:03
 * @description 决策定义查询参数
 */
@Data
@Schema(description = "决策定义DTO，用于查询决策定义")
public class DecisionDefinitionDTO extends PageDTO implements Serializable {

    @Schema(description = "决策定义key", example = "123456789")
    private Long decisionKey;

    @Schema(description = "决策的唯一ID", example = "decision-id-123")
    private String decisionId;

    @Schema(description = "决策名称", example = "我的决策")
    private String decisionName;

    @Schema(description = "决策版本号", example = "1")
    private Integer version;

    @Schema(description = "决策需求的唯一标识", example = "987654321")
    private Long decisionRequirementsKey;

    @Schema(description = "决策需求的唯一ID", example = "decision-req-id-456")
    private String decisionRequirementsId;
}
