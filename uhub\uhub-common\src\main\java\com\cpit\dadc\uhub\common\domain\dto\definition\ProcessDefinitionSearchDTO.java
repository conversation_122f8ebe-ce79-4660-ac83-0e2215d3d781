package com.cpit.dadc.uhub.common.domain.dto.definition;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-09-18
 **/
@Data
public class ProcessDefinitionSearchDTO extends PageDTO implements Serializable {
    @Schema(description = "流程定义key")
    private String procDefKey;
    @Schema(description = "流程定义名")
    private String procDefName;
}
