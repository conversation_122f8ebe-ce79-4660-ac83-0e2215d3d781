package com.cpit.dadc.uhub.common.domain.dto.definition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @description:
 * @date: 2024-09-18
 **/
@Data
public class StartProcessDTO implements Serializable {
    @Schema(description = "流程定义id,必填")
    private Long procDefKey;
    @Schema(description = "要添加的变量")
    private Map<String, Object> variables;
}