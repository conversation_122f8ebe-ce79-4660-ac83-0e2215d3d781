package com.cpit.dadc.uhub.common.domain.dto.delpoy;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;


@Data
public class ProcessEntityDTO extends PageDTO implements Serializable {

    @Schema(description = "流程定义key")
    private Long ProcessDefinitionKey;

    @Schema(description = "流程定义id")
    private String bpmnProcessId;

    @Schema(description = "流程定义版本")
    private Integer version;

    @Schema(description = "资源名")
    private String resourceName;

    @Schema(description = "流程定义的校验和")
    private String checksum;

    @Schema(description = "开始时间")
    private Date startDate;

    @Schema(description = "结束时间")
    private Date endDate;

}
