package com.cpit.dadc.uhub.common.domain.dto.design;

import com.cpit.dadc.uhub.common.valid.group.BaseGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-08-01
 **/
@Data
public class ProcessDesignDTO implements Serializable {
    @Schema(description = "id,新增时无需添加修改时必填", required = false, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @NotBlank(message = "id不能为空", groups = {BaseGroup.Update.class})
    private String id;

    @Schema(description = "流程名称或决策名称", required = true, maxLength = 32)
    @NotBlank(message = "流程名称或决策名称不能为空", groups = {BaseGroup.Insert.class, BaseGroup.Update.class})
    @Length(max = 32, message = "流程或决策名称长度不能超过32", groups = {BaseGroup.Insert.class})
    private String processName;

    @Schema(description = "流程定义key", required = true)
    @NotBlank(message = "流程定义key不能为空", groups = {BaseGroup.Insert.class})
    private String processKey;

    @Schema(description = "业务类型", required = true)
    @NotBlank(message = "业务类型不能为空", groups = {BaseGroup.Insert.class, BaseGroup.Update.class})
    private String businessType;

    @Schema(description = "区分是dmn还是bpmn", required = true)
    @NotBlank(message = "type不能为空", groups = {BaseGroup.Insert.class, BaseGroup.Update.class})
    private String type;

    @Schema(description = "流程定义的xml")
    private String processXml;

    @Schema(description = "流程定义的yml")
    private String processYml;
}
