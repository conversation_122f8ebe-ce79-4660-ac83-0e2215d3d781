package com.cpit.dadc.uhub.common.domain.dto.design;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-08-02
 **/
@Data
public class ProcessDesignSearchDTO extends PageDTO implements Serializable {
    @Schema(description = "id")
    private String id;

    @Schema(description = "流程名称")
    private String processName;

    @Schema(description = "流程定义key")
    private String processKey;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "区分是dmn还是bpmn")
    private String type;

    @Schema(description = "流程是否发布(0:未发布; 1:已发布)")
    private Integer processStatus;

}