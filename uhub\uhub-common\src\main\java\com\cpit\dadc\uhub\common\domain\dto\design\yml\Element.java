package com.cpit.dadc.uhub.common.domain.dto.design.yml;

import com.cpit.dadc.uhub.common.domain.vo.design.flow.XYPosition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @date: 2024-08-14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Element implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "节点名")
    private String name;
    @Schema(description = "节点类型，可选类型请参考BpmnElementType")
    private String elementType;
    @Schema(description = "incoming")
    private String incoming;
    @Schema(description = "outgoings")
    private List<String> outgoings;
    @Schema(description = "condition")
    private String condition;
    @Schema(description = "modelerTemplate")
    private String modelerTemplate;
    @Schema(description = "modelerTemplateVersion")
    private String modelerTemplateVersion;
    @Schema(description = "modelerTemplateIcon")
    private String modelerTemplateIcon;
    @Schema(description = "position")
    private XYPosition position;
    @Schema(description = "extensionElements")
    private ExtensionElements extensionElements;
}
