package com.cpit.dadc.uhub.common.domain.dto.design.yml;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @date: 2024-08-14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExtensionElements implements Serializable {
    @Schema(description = "assignee")
    private String assignee;
    @Schema(description = "candidateGroups")
    private String candidateGroups;
    @Schema(description = "candidateUsers")
    private String candidateUsers;
    @Schema(description = "type")
    private String type;
    @Schema(description = "retries")
    @Builder.Default
    private String retries = "3";
    @Schema(description = "input")
    private List<IoMapping> inputs;
    @Schema(description = "outputs")
    private List<IoMapping> outputs;
    @Schema(description = "taskHeaders")
    private List<TaskHeader> taskHeaders;
}
