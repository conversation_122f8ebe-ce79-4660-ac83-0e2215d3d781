package com.cpit.dadc.uhub.common.domain.dto.design.yml;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-08-14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IoMapping implements Serializable {
    @Schema(description = "source")
    private String source;
    @Schema(description = "target")
    private String target;
}
