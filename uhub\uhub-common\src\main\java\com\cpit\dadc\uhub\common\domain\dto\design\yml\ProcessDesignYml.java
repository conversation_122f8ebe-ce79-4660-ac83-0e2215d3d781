package com.cpit.dadc.uhub.common.domain.dto.design.yml;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @date: 2024-08-14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProcessDesignYml implements Serializable {
    @Schema(description = "id")
    private String id;
    @Schema(description = "流程名称")
    private String name;
    @Schema(description = "元素")
    private List<Element> elements;
}
