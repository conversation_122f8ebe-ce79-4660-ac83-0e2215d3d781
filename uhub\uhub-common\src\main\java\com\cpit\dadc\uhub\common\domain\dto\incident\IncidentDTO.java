package com.cpit.dadc.uhub.common.domain.dto.incident;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import com.cpit.dadc.uhub.common.domain.entity.bpm.ErrorType;
import com.cpit.dadc.uhub.common.domain.entity.bpm.IncidentState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 错误事件DTO
 * @date: 2024-08-01
 **/
@Data
public class IncidentDTO extends PageDTO implements Serializable {

    @Schema(description = "事件Key", required = false)
    private long key;

    @Schema(description = "开始时间", required = false)
    private String startDate;

    @Schema(description = "结束时间", required = false)
    private String endDate;

    @Schema(description = "BPMN流程ID", required = false)
    private String bpmnProcessId;

    @Schema(description = "错误类型", required = false)
    private ErrorType errorType;

    @Schema(description = "错误信息", required = false)
    private String errorMessage;

    @Schema(description = "事件状态", required = false)
    private IncidentState state;

    @Schema(description = "流程节点ID", required = false)
    private String flowNodeId;

    @Schema(description = "流程节点实例Key", required = false)
    private Long flowNodeInstanceKey;

    @Schema(description = "任务Key", required = false)
    private Long jobKey;

    @Schema(description = "流程实例Key", required = false)
    private Long processInstanceKey;

    @Schema(description = "流程定义Key", required = false)
    private Long processDefinitionKey;

}
