package com.cpit.dadc.uhub.common.domain.dto.process;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import com.cpit.dadc.uhub.common.domain.entity.bpm.ProcessInstanceState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

@Data
public class ProcessInstanceSearchDTO extends PageDTO {

    @Schema(description = "流程实例key")
    private Long processInstanceKey;

    @Schema(description = "流程定义key")
    private Long processDefinitionKey;

    @Schema(description = "流程bpmnProcessId")
    private String bpmnProcessId;

    @Schema(description = "是否是当前用户发起")
    private Boolean isMine = false;

    @Schema(description = "流程状态")
    private ProcessInstanceState state;

    @Schema(description = "开始时间，Unix 时间戳")
    private Long startDate;

    @Schema(description = "结束时间，Unix 时间戳")
    private Long endDate;

    // 自动转换字符串日期为 Unix 时间戳（前端传入的日期）
    public void setStartDate(String startDateStr) {
        if (startDateStr != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date date = sdf.parse(startDateStr);
                this.startDate = date.getTime(); // 将日期转换为 Unix 时间戳（毫秒）
            } catch (Exception e) {
                e.printStackTrace();
                this.startDate = null;  // 发生异常时设置为 null
            }
        }
    }

    public void setEndDate(String endDateStr) {
        if (endDateStr != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date date = sdf.parse(endDateStr);
                // 设置为当天的 23:59:59
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                this.endDate = calendar.getTimeInMillis(); // 将日期转换为 Unix 时间戳（毫秒）
            } catch (Exception e) {
                e.printStackTrace();
                this.endDate = null;  // 发生异常时设置为 null
            }
        }
    }
}
