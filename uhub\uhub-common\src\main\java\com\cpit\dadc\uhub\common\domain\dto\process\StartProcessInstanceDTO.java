package com.cpit.dadc.uhub.common.domain.dto.process;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StartProcessInstanceDTO implements Serializable {

    @NotNull(message = "processDefinitionKey不能为空")
    @Schema(description = "流程定义的key")
    private Long processDefinitionKey;

    @Schema(description = "流程变量")
    private Map<String, Object> variables;

}
