package com.cpit.dadc.uhub.common.domain.dto.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CPITExecutionStatisticalDTO implements Serializable {

    @Schema(description =  "开始时间，yyyy-MM-dd")
    private String startDate;

    @Schema(description =  "结束时间，格式yyyy-MM-dd")
    private String endDate;

    @Schema(description = "流程设计ID列表", required = false, type = "array", example = "[\"process1\", \"process2\"]")
    private List<String> bpmnProcessIds;


}
