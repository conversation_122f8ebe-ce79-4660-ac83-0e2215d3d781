package com.cpit.dadc.uhub.common.domain.dto.statistics;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 流程执行实例DTO
 * @date: 2024-08-01
 **/
@Data
public class ExecutionInstanceDTO extends PageDTO implements Serializable {

    @Schema(description = "主键ID", required = false)
    private String id;

    @Schema(description = "开始时间", required = false)
    private String startDate;

    @Schema(description = "结束时间", required = false)
    private String endDate;

    @Schema(description = "流程节点实例ID", required = false)
    private Long flowNodeInstanceId;

    @Schema(description = "流程节点ID", required = false)
    private String flowNodeId;

    @Schema(description = "流程节点名称", required = false)
    private String flowNodeName;

    @Schema(description = "流程实例Key", required = false)
    private Long processInstanceKey;

    @Schema(description = "流程定义Key", required = false)
    private Long processDefinitionKey;

    @Schema(description = "BPMN流程ID", required = false)
    private String bpmnProcessId;

    @Schema(description = "流程节点状态", required = false)
    private String state;

    @Schema(description = "持续时间", required = false)
    private Long duration;
}