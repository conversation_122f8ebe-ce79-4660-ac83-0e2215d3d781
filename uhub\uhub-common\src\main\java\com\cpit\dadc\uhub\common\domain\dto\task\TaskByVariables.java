package com.cpit.dadc.uhub.common.domain.dto.task;

enum Operator {
    eq
}

public class TaskByVariables {

    private String name;

    private String value;

    private Operator operator;

    public String getName() {
        return name;
    }

    public TaskByVariables setName(String name) {
        this.name = name;
        return this;
    }

    public String getValue() {
        return value;
    }

    public TaskByVariables setValue(String value) {
        this.value = value;
        return this;
    }

    public Operator getOperator() {
        return operator;
    }

    public TaskByVariables setOperator(String operator) {
        this.operator = Operator.valueOf(operator);
        return this;
    }
}
