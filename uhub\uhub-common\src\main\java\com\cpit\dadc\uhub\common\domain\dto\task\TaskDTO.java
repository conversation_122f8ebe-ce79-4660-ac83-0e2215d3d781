package com.cpit.dadc.uhub.common.domain.dto.task;

import cn.hutool.core.util.ObjectUtil;
import com.cpit.dadc.uhub.common.domain.dto.varibale.VariableDTO;
import com.cpit.dadc.uhub.common.domain.entity.bpm.Task;
import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskImplementation;
import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskState;
import com.cpit.dadc.uhub.common.domain.vo.task.TaskVO;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;
import java.util.StringJoiner;

import static com.cpit.dadc.uhub.common.utils.CollectionUtil.toArrayOfStrings;

public final class TaskDTO {

    private String id;

    private String taskId;

    private String processInstanceId;

    /**
     * Field is used to resolve task name.
     */
    private String flowNodeBpmnId;

    private Long flowNodeInstanceId;

    /**
     * Field is used to resolve process name.
     */
    private Long processDefinitionId;

    /**
     * Fallback value for process name.
     */
    private Long bpmnProcessId;

    private String creationTime;
    private String completionTime;
    private String assignee;
    private String[] candidateGroups;
    private String[] candidateUsers;
    private TaskState taskState;
    private String[] sortValues;
    private boolean isFirst = false;
    private String formKey;
    private String formId;
    private Long formVersion;
    private Boolean isFormEmbedded;
    private String tenantId;
    private LocalDateTime dueDate;
    private LocalDateTime followUpDate;


    private VariableDTO[] variables;

    private TaskImplementation implementation;

    public String getId() {
        return id;
    }

    public TaskDTO setId(String id) {
        this.id = id;
        return this;
    }

    public String getTaskId() {
        return taskId;
    }

    public TaskDTO setTaskId(String taskId) {
        this.taskId = taskId;
        return this;
    }

    public String getAssignee() {
        return assignee;
    }

    public TaskDTO setAssignee(String assignee) {
        this.assignee = assignee;
        return this;
    }

    public String[] getCandidateGroups() {
        return candidateGroups;
    }

    public TaskDTO setCandidateGroups(final String[] candidateGroups) {
        this.candidateGroups = candidateGroups;
        return this;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public TaskDTO setProcessInstanceId(final String processInstanceId) {
        this.processInstanceId = processInstanceId;
        return this;
    }

    public String getFlowNodeBpmnId() {
        return flowNodeBpmnId;
    }

    public TaskDTO setFlowNodeBpmnId(String flowNodeBpmnId) {
        this.flowNodeBpmnId = flowNodeBpmnId;
        return this;
    }

    public Long getFlowNodeInstanceId() {
        return flowNodeInstanceId;
    }

    public TaskDTO setFlowNodeInstanceId(final Long flowNodeInstanceId) {
        this.flowNodeInstanceId = flowNodeInstanceId;
        return this;
    }

    public Long getProcessDefinitionId() {
        return processDefinitionId;
    }

    public TaskDTO setProcessDefinitionId(Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
        return this;
    }

    public Long getBpmnProcessId() {
        return bpmnProcessId;
    }

    public TaskDTO setBpmnProcessId(Long bpmnProcessId) {
        this.bpmnProcessId = bpmnProcessId;
        return this;
    }

    public String getCreationTime() {
        return creationTime;
    }

    public TaskDTO setCreationTime(String creationTime) {
        this.creationTime = creationTime;
        return this;
    }

    public String getCompletionTime() {
        return completionTime;
    }

    public TaskDTO setCompletionTime(String completionTime) {
        this.completionTime = completionTime;
        return this;
    }

    public TaskState getTaskState() {
        return taskState;
    }

    public TaskDTO setTaskState(TaskState taskState) {
        this.taskState = taskState;
        return this;
    }

    public String[] getSortValues() {
        return sortValues;
    }

    public TaskDTO setSortValues(final String[] sortValues) {
        this.sortValues = sortValues;
        return this;
    }

    public boolean getIsFirst() {
        return isFirst;
    }

    public TaskDTO setIsFirst(final boolean first) {
        isFirst = first;
        return this;
    }

    public String[] candidateUsers() {
        return candidateUsers;
    }

    public TaskDTO setCandidateUsers(String[] candidateUsers) {
        this.candidateUsers = candidateUsers;
        return this;
    }

    public String[] getCandidateUsers() {
        return candidateUsers;
    }

    public String getFormKey() {
        return formKey;
    }

    public TaskDTO setFormKey(final String formKey) {
        this.formKey = formKey;
        return this;
    }

    public String getFormId() {
        return formId;
    }

    public TaskDTO setFormId(String formId) {
        this.formId = formId;
        return this;
    }

    public Long getFormVersion() {
        return formVersion;
    }

    public TaskDTO setFormVersion(Long formVersion) {
        this.formVersion = formVersion;
        return this;
    }

    public Boolean getIsFormEmbedded() {
        return isFormEmbedded;
    }

    public TaskDTO setIsFormEmbedded(Boolean isFormEmbedded) {
        this.isFormEmbedded = isFormEmbedded;
        return this;
    }

    public String getTenantId() {
        return tenantId;
    }

    public TaskDTO setTenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public TaskDTO setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
        return this;
    }

    public LocalDateTime getFollowUpDate() {
        return followUpDate;
    }

    public TaskDTO setFollowUpDate(LocalDateTime followUpDate) {
        this.followUpDate = followUpDate;
        return this;
    }

    public VariableDTO[] getVariables() {
        return variables;
    }

    public TaskDTO setVariables(VariableDTO[] variables) {
        this.variables = variables;
        return this;
    }

    public TaskImplementation getImplementation() {
        return implementation;
    }

    public TaskDTO setImplementation(TaskImplementation implementation) {
        this.implementation = implementation;
        return this;
    }

    public static TaskDTO createFrom(Task task, ObjectMapper objectMapper) {
        return createFrom(task, null, objectMapper);
    }

    public static TaskDTO createFrom(
            Task task, Object[] sortValues, ObjectMapper objectMapper) {
        final TaskDTO taskDTO =
                new TaskDTO()
                        .setCreationTime(task.getCreationTime().toString())
                        .setCompletionTime(ObjectUtil.isNotEmpty(task.getCompletionTime()) ? task.getCompletionTime().toString() : null)
                        .setId(task.getId())
                        .setProcessInstanceId(String.valueOf(task.getProcessInstanceKey()))
                        .setTaskState(task.getState())
                        .setAssignee(task.getAssignee())
                        .setBpmnProcessId(task.getBpmnProcessId())
                        .setProcessDefinitionId(task.getProcessDefinitionKey())
                        .setFlowNodeBpmnId(task.getFlowNodeBpmnId())
                        .setFlowNodeInstanceId(task.getFlowNodeInstanceId())
                        .setFormKey(task.getFormKey())
                        .setFormId(task.getFormId())
                        .setFormVersion(task.getFormVersion())
                        .setIsFormEmbedded(task.getIsFormEmbedded())
                        .setTenantId(task.getTenantId())
                        .setFollowUpDate(task.getFollowUpDate())
                        .setDueDate(task.getDueDate())
                        .setCandidateGroups(task.getCandidateGroups())
                        .setCandidateUsers(task.getCandidateUsers())
                        .setImplementation(task.getImplementation());
        if (sortValues != null) {
            taskDTO.setSortValues(toArrayOfStrings(sortValues));
        }
        return taskDTO;
    }

    public static TaskDTO createFrom(
            TaskVO taskSearchView, VariableDTO[] variables, ObjectMapper objectMapper) {
        return new TaskDTO()
                .setId(taskSearchView.getId())
                .setTaskId(taskSearchView.getTaskId())
                .setCreationTime(taskSearchView.getCreationTime().toString())
                .setCompletionTime(ObjectUtil.isNotEmpty(taskSearchView.getCompletionTime()) ? taskSearchView.getCompletionTime().toString() : null)
                .setId(taskSearchView.getId())
                .setProcessInstanceId(taskSearchView.getProcessInstanceId())
                .setTaskState(taskSearchView.getState())
                .setAssignee(taskSearchView.getAssignee())
                .setBpmnProcessId(taskSearchView.getBpmnProcessId())
                .setProcessDefinitionId(taskSearchView.getProcessDefinitionId())
                .setFlowNodeBpmnId(taskSearchView.getFlowNodeBpmnId())
                .setFlowNodeInstanceId(taskSearchView.getFlowNodeInstanceId())
                .setFormKey(taskSearchView.getFormKey())
                .setFormId(taskSearchView.getFormId())
                .setFormVersion(taskSearchView.getFormVersion())
                .setIsFormEmbedded(taskSearchView.getIsFormEmbedded())
                .setTenantId(taskSearchView.getTenantId())
                .setFollowUpDate(taskSearchView.getFollowUpDate())
                .setDueDate(taskSearchView.getDueDate())
                .setCandidateGroups(taskSearchView.getCandidateGroups())
                .setCandidateUsers(taskSearchView.getCandidateUsers())
                .setVariables(variables)
                .setImplementation(taskSearchView.getImplementation());
    }

   /* public static Task toTaskEntity(TaskDTO taskDTO) {
        Task.TaskEntityBuilder builder = Task.builder()
                .id(taskDTO.getId())
                .processInstanceKey(Long.valueOf(taskDTO.getProcessInstanceId()))
                .state(taskDTO.getTaskState())
                .assignee(taskDTO.getAssignee())
                .bpmnProcessId(taskDTO.getBpmnProcessId())
                .processDefinitionKey(taskDTO.getProcessDefinitionId())
                .flowNodeBpmnId(taskDTO.getFlowNodeBpmnId())
                .flowNodeInstanceId(taskDTO.getFlowNodeInstanceId())
                .formKey(taskDTO.getFormKey())
                .formId(taskDTO.getFormId())
                .formVersion(taskDTO.getFormVersion())
                .isFormEmbedded(taskDTO.getIsFormEmbedded())
                .followUpDate(taskDTO.getFollowUpDate())
                .dueDate(taskDTO.getDueDate())
                .candidateGroups(taskDTO.getCandidateGroups())
                .candidateUsers(taskDTO.getCandidateUsers())
                .implementation(taskDTO.getImplementation());

        try {
            LocalDateTime creationTime = DateUtil.toOffsetDateTime(
                    DateUtil.SIMPLE_DATE_FORMAT.parse(taskDTO.getCreationTime()).toInstant()
            ).toLocalDateTime();
            builder.creationTime(creationTime);

            if (taskDTO.getCompletionTime() != null) {
                LocalDateTime completionTime = DateUtil.toOffsetDateTime(
                        DateUtil.SIMPLE_DATE_FORMAT.parse(taskDTO.getCompletionTime()).toInstant()
                ).toLocalDateTime();
                builder.completionTime(completionTime);
            }
        } catch (ParseException e) {
            throw new TaskRuntimeException(e);
        }

        return builder.build();
    }*/

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final TaskDTO taskDTO = (TaskDTO) o;
        return isFirst == taskDTO.isFirst
                && implementation == taskDTO.implementation
                && Objects.equals(id, taskDTO.id)
                && Objects.equals(processInstanceId, taskDTO.processInstanceId)
                && Objects.equals(flowNodeBpmnId, taskDTO.flowNodeBpmnId)
                && Objects.equals(flowNodeInstanceId, taskDTO.flowNodeInstanceId)
                && Objects.equals(processDefinitionId, taskDTO.processDefinitionId)
                && Objects.equals(bpmnProcessId, taskDTO.bpmnProcessId)
                && Objects.equals(creationTime, taskDTO.creationTime)
                && Objects.equals(completionTime, taskDTO.completionTime)
                && Objects.equals(assignee, taskDTO.assignee)
                && Arrays.equals(candidateGroups, taskDTO.candidateGroups)
                && Arrays.equals(candidateUsers, taskDTO.candidateUsers)
                && taskState == taskDTO.taskState
                && Arrays.equals(sortValues, taskDTO.sortValues)
                && Objects.equals(formKey, taskDTO.formKey)
                && Objects.equals(formId, taskDTO.formId)
                && Objects.equals(formVersion, taskDTO.formVersion)
                && Objects.equals(isFormEmbedded, taskDTO.isFormEmbedded)
                && Objects.equals(tenantId, taskDTO.tenantId)
                && Objects.equals(dueDate, taskDTO.dueDate)
                && Objects.equals(followUpDate, taskDTO.followUpDate)
                && Arrays.equals(variables, taskDTO.variables);
    }

    @Override
    public int hashCode() {
        int result =
                Objects.hash(
                        id,
                        processInstanceId,
                        flowNodeBpmnId,
                        flowNodeInstanceId,
                        processDefinitionId,
                        bpmnProcessId,
                        creationTime,
                        completionTime,
                        assignee,
                        taskState,
                        isFirst,
                        formKey,
                        formId,
                        formVersion,
                        isFormEmbedded,
                        tenantId,
                        dueDate,
                        followUpDate,
                        implementation);
        result = 31 * result + Arrays.hashCode(candidateGroups);
        result = 31 * result + Arrays.hashCode(candidateUsers);
        result = 31 * result + Arrays.hashCode(sortValues);
        result = 31 * result + Arrays.hashCode(variables);
        return result;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", TaskDTO.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("processInstanceId='" + processInstanceId + "'")
                .add("flowNodeBpmnId='" + flowNodeBpmnId + "'")
                .add("flowNodeInstanceId='" + flowNodeInstanceId + "'")
                .add("processDefinitionId='" + processDefinitionId + "'")
                .add("bpmnProcessId='" + bpmnProcessId + "'")
                .add("creationTime='" + creationTime + "'")
                .add("completionTime='" + completionTime + "'")
                .add("assignee='" + assignee + "'")
                .add("candidateGroups=" + Arrays.toString(candidateGroups))
                .add("candidateUsers=" + Arrays.toString(candidateUsers))
                .add("taskState=" + taskState)
                .add("sortValues=" + Arrays.toString(sortValues))
                .add("isFirst=" + isFirst)
                .add("formKey='" + formKey + "'")
                .add("formId='" + formId + "'")
                .add("formVersion='" + formVersion + "'")
                .add("isFormEmbedded='" + isFormEmbedded + "'")
                .add("tenantId='" + tenantId + "'")
                .add("dueDate=" + dueDate)
                .add("followUpDate=" + followUpDate)
                .add("variables=" + Arrays.toString(variables))
                .add("implementation=" + implementation)
                .toString();
    }
}