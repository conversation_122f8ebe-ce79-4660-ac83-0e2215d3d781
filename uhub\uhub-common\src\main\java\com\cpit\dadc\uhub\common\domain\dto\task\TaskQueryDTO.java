package com.cpit.dadc.uhub.common.domain.dto.task;

import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskState;
import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskImplementation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TaskQueryDTO {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "任务状态")
    private TaskState state;

    @Schema(description = "是否已分配")
    private Boolean assigned;

    @Schema(description = "任务分配人")
    private String assignee;

    @Schema(description = "任务分配人列表")
    private String[] assignees;

    @Schema(description = "任务定义ID")
    private String taskDefinitionId;

    @Schema(description = "候选组")
    private String candidateGroup;

    @Schema(description = "候选组列表")
    private String[] candidateGroups;

    @Schema(description = "候选用户")
    private String candidateUser;

    @Schema(description = "候选用户列表")
    private String[] candidateUsers;

    @Schema(description = "流程定义ID")
    private String processDefinitionId;

    @Schema(description = "流程实例ID")
    private String processInstanceId;

    @Schema(description = "每页大小")
    private int pageSize;

    @Schema(description = "任务变量")
    private TaskByVariables[] taskVariables;

    @Schema(description = "租户ID列表")
    private String[] tenantIds;

    @Schema(description = "搜索后的值列表")
    private String[] searchAfter;

    @Schema(description = "搜索后或等于的值列表")
    private String[] searchAfterOrEqual;

    @Schema(description = "搜索前的值列表")
    private String[] searchBefore;

    @Schema(description = "搜索前或等于的值列表")
    private String[] searchBeforeOrEqual;

    @Schema(description = "跟进日期")
    private LocalDateTime followUpDate;

    @Schema(description = "截止日期")
    private LocalDateTime dueDate;

    @Schema(description = "候选用户或组")
    private TaskByCandidateUserOrGroup taskByCandidateUserOrGroups;

    @Schema(description = "任务实现")
    private TaskImplementation implementation;

    @Schema(description = "流程节点实例ID")
    private String flowNodeInstanceId;
}