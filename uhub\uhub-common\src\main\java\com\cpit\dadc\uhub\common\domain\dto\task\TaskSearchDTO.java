package com.cpit.dadc.uhub.common.domain.dto.task;

import com.cpit.dadc.uhub.common.domain.dto.PageDTO;
import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskState;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
public class TaskSearchDTO extends PageDTO implements Serializable {

    @Schema(description = "任务状态")
    private TaskState state;

    @Schema(description = "是否已分配")
    private Boolean assigned;

    @Schema(description = "任务分配人")
    private String assignee;

    @Schema(description = "任务分配人列表")
    private String[] assignees;

    @Schema(description = "流程定义ID")
    private String processDefinitionId;

    @Schema(description = "流程实例ID")
    private Long processInstanceId;

    @Schema(description = "截止日期开始时间")
    private LocalDateTime dueDateStart;

    @Schema(description = "截止日期结束时间")
    private LocalDateTime dueDateEnd;

}