package com.cpit.dadc.uhub.common.domain.dto.varibale;

import com.cpit.dadc.uhub.common.domain.entity.bpm.Task;
import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskState;
import com.cpit.dadc.uhub.common.domain.vo.task.TaskVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GetVariablesRequest implements Serializable {

    private String taskId;
    private TaskState state;
    private Long flowNodeInstanceId;
    private String processInstanceId;
    private List<String> varNames;

    public static GetVariablesRequest createFrom(Task task) {
        return GetVariablesRequest.builder()
                .taskId(task.getId())
                .flowNodeInstanceId(task.getFlowNodeInstanceId())
                .state(task.getState())
                .processInstanceId(String.valueOf(task.getProcessInstanceKey()))
                .build();
    }

    public static GetVariablesRequest createFrom(
            TaskVO taskSearchView, List<String> varNames) {
        return new GetVariablesRequest()
                .setTaskId(taskSearchView.getId())
                .setFlowNodeInstanceId(taskSearchView.getFlowNodeInstanceId())
                .setState(taskSearchView.getState())
                .setProcessInstanceId(taskSearchView.getProcessInstanceId())
                .setVarNames(varNames);
    }

    public GetVariablesRequest setVarNames(final List<String> varNames) {
        this.varNames = varNames;
        return this;
    }


    public String getTaskId() {
        return taskId;
    }

    public GetVariablesRequest setTaskId(final String taskId) {
        this.taskId = taskId;
        return this;
    }

    public TaskState getState() {
        return state;
    }

    public GetVariablesRequest setState(final TaskState state) {
        this.state = state;
        return this;
    }

    public Long getFlowNodeInstanceId() {
        return flowNodeInstanceId;
    }

    public GetVariablesRequest setFlowNodeInstanceId(final Long flowNodeInstanceId) {
        this.flowNodeInstanceId = flowNodeInstanceId;
        return this;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public GetVariablesRequest setProcessInstanceId(final String processInstanceId) {
        this.processInstanceId = processInstanceId;
        return this;
    }

    public List<String> getVarNames() {
        return varNames;
    }





}
