package com.cpit.dadc.uhub.common.domain.dto.varibale;

import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskVariable;
import com.cpit.dadc.uhub.common.domain.entity.bpm.Variable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class VariableDTO {

     private String id;
     private String name;
     private String value;
     private boolean isValueTruncated;
     private String previewValue;

    public String getId() {
        return id;
    }

    public VariableDTO setId(final String id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public VariableDTO setName(final String name) {
        this.name = name;
        return this;
    }

    public String getValue() {
        return value;
    }

    public VariableDTO setValue(final String value) {
        this.value = value;
        return this;
    }

    public boolean getIsValueTruncated() {
        return isValueTruncated;
    }

    public VariableDTO setIsValueTruncated(final boolean valueTruncated) {
        isValueTruncated = valueTruncated;
        return this;
    }

    public String getPreviewValue() {
        return previewValue;
    }

    public VariableDTO setPreviewValue(final String previewValue) {
        this.previewValue = previewValue;
        return this;
    }

    public static VariableDTO createFrom(TaskVariable variableEntity) {
        final VariableDTO variableDTO =
                new VariableDTO().setId(variableEntity.getId()).setName(variableEntity.getName());
        variableDTO
                .setPreviewValue(variableEntity.getValue())
                .setIsValueTruncated(variableEntity.getIsPreview())
                .setValue(variableEntity.getFullValue());
        return variableDTO;
    }

    public static VariableDTO createFrom(Variable variable) {
        final VariableDTO variableDTO =
                new VariableDTO().setId(variable.getId()).setName(variable.getName());
        variableDTO
                .setPreviewValue(variable.getValue())
                .setIsValueTruncated(variable.isPreview())
                .setValue(variable.getFullValue());
        return variableDTO;
    }

    public static List<VariableDTO> createFrom(List<Variable> variableEntities) {
        final List<VariableDTO> result = new ArrayList<>();
        if (variableEntities != null) {
            for (Variable variable : variableEntities) {
                if (variable != null) {
                    result.add(createFrom(variable));
                }
            }
        }
        return result;
    }

    public static List<VariableDTO> createFromTaskVariables(
            List<TaskVariable> taskVariableEntities) {
        return taskVariableEntities.stream().map(VariableDTO::createFrom).collect(Collectors.toList());
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final VariableDTO that = (VariableDTO) o;
        return isValueTruncated == that.isValueTruncated
                && Objects.equals(id, that.id)
                && Objects.equals(name, that.name)
                && Objects.equals(value, that.value)
                && Objects.equals(previewValue, that.previewValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, value, isValueTruncated, previewValue);
    }
}
