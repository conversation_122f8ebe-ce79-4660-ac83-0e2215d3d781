package com.cpit.dadc.uhub.common.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id*/
//    @TableId
//    @TableField(value = "id")
//    private String id;

    /** 创建用户id*/
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 更新用户id*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 创建时间*/
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String tenantId;
}
