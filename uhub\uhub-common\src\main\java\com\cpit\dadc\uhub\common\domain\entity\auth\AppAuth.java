package com.cpit.dadc.uhub.common.domain.entity.auth;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @description:
 * @date: 2024-08-29
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("app_auth")
public class AppAuth extends BaseEntity {
    /** id*/
    @TableId
    @TableField(value = "id")
    private String id;
    /** 访问秘钥 */
    private String accessKey;

    /** 秘钥*/
    private String secretKey;

    /** 盐值*/
    private String salt;

    /** 租户id*/
    private String tenantId;

    /** 应用id*/
    private String projectId;

    /** 业务方项目立项名称*/
    private String projectName;

    /** 秘钥到期时间*/
    private LocalDateTime expireTime;

    /** 状态，#0:已禁用，#1:启用*/
    private Integer status;
}
