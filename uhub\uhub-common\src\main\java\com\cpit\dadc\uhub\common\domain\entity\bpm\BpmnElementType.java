package com.cpit.dadc.uhub.common.domain.entity.bpm;

public enum BpmnElementType {
    UNSPECIFIED,
    UNKNOWN,

    PROCESS,
    SUB_PROCESS,
    EVENT_SUB_PROCESS,
    START_EVENT,
    INTERMEDIATE_CATCH_EVENT,
    INTERMEDIATE_THROW_EVENT,
    BOUNDARY_EVENT,
    END_EVENT,
    SERVICE_TASK,
    RECEIVE_TASK,
    MANU<PERSON>_TASK,
    EXCLUSIVE_GATEWAY,
    PARALLEL_GATEWAY,
    EVENT_BASED_GATEWAY,
    SEQUENCE_FLOW,
    MULTI_INSTANCE_BODY,
    CALL_ACTIVITY,
    USER_TASK,
    BUSINESS_RULE_TASK,
    SCRIPT_TASK,
    SEND_TASK,
    INCLUSIVE_GATEWAY;
}