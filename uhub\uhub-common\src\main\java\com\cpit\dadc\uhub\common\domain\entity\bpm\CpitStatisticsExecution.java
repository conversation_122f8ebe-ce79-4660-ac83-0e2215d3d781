package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("cpit_statistics_execution") // 指定表名
public class CpitStatisticsExecution {

    @TableId(value = "id")
    private String id;

    @TableField("year") // 年
    private Integer year;

    @TableField("month") // 月份
    private Short month;

    @TableField("date") // 日期
    private String date;

    @TableField("process_definition_key") // 流程定义key
    private Long processDefinitionKey;

    @TableField("bpmn_process_id") // 流程设计id
    private String bpmnProcessId;

    @TableField("complete_count") // 完成数量
    private Long completeCount;

    @TableField("active_count") // 激活数量
    private Long activeCount;

    @TableField("failed_count") // 失败数量
    private Long failedCount;

    @TableField("create_time") // 开始时间
    private LocalDateTime createTime;

    @TableField("tenant_id") // 租户
    private String tenantId;
}