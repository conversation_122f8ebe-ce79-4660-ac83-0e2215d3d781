package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_decision_evaluation")
public class DecisionEvaluation extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    private long key;
    private Integer executionIndex;
    private DecisionInstanceState state;
    private LocalDateTime evaluationDate;
    private String evaluationFailure;
    private Long position;
    private int partitionId;
    private long decisionRequirementsKey;
    private String decisionRequirementsId;
    private long processDefinitionKey;
    private long processInstanceKey;

    private String bpmnProcessId;

    private long elementInstanceKey;
    private String elementId;
    private String decisionId;
    private String decisionDefinitionId;
    private String decisionName;
    private int decisionVersion;
    private String rootDecisionName;
    private String rootDecisionId;
    private String rootDecisionDefinitionId;
    private DecisionType decisionType;
    private String result;
    @TableField(exist = false)
    private List<DecisionEvaluationInput> evaluatedInputs = new ArrayList<>();
    @TableField(exist = false)
    private List<DecisionEvaluationOutput> evaluatedOutputs = new ArrayList<>();

    @JsonIgnore
    private Object[] sortValues;

    public static Long extractKey(String id) {
        return Long.valueOf(id.split("-")[0]);
    }

    public DecisionEvaluation setId(Long key, int executionIndex) {
        id = String.format("%d-%d", key, executionIndex);
        return this;
    }

    public Integer getExecutionIndex() {
        return executionIndex;
    }

    public DecisionEvaluation setExecutionIndex(Integer executionIndex) {
        this.executionIndex = executionIndex;
        return this;
    }

    public DecisionInstanceState getState() {
        return state;
    }

    public DecisionEvaluation setState(final DecisionInstanceState state) {
        this.state = state;
        return this;
    }

    public LocalDateTime getEvaluationDate() {
        return evaluationDate;
    }

    public DecisionEvaluation setEvaluationDate(final LocalDateTime evaluationDate) {
        this.evaluationDate = evaluationDate;
        return this;
    }

    public String getEvaluationFailure() {
        return evaluationFailure;
    }

    public DecisionEvaluation setEvaluationFailure(final String evaluationFailure) {
        this.evaluationFailure = evaluationFailure;
        return this;
    }

    public Long getPosition() {
        return position;
    }

    public DecisionEvaluation setPosition(final Long position) {
        this.position = position;
        return this;
    }

    public String getDecisionDefinitionId() {
        return decisionDefinitionId;
    }

    public DecisionEvaluation setDecisionDefinitionId(final String decisionDefinitionId) {
        this.decisionDefinitionId = decisionDefinitionId;
        return this;
    }

    public long getDecisionRequirementsKey() {
        return decisionRequirementsKey;
    }

    public DecisionEvaluation setDecisionRequirementsKey(final long decisionRequirementsKey) {
        this.decisionRequirementsKey = decisionRequirementsKey;
        return this;
    }

    public String getDecisionRequirementsId() {
        return decisionRequirementsId;
    }

    public DecisionEvaluation setDecisionRequirementsId(final String decisionRequirementsId) {
        this.decisionRequirementsId = decisionRequirementsId;
        return this;
    }

    public long getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public DecisionEvaluation setProcessDefinitionKey(final long processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
        return this;
    }

    public long getProcessInstanceKey() {
        return processInstanceKey;
    }

    public DecisionEvaluation setProcessInstanceKey(final long processInstanceKey) {
        this.processInstanceKey = processInstanceKey;
        return this;
    }

    public String getBpmnProcessId() {
        return bpmnProcessId;
    }

    public DecisionEvaluation setBpmnProcessId(String bpmnProcessId) {
        this.bpmnProcessId = bpmnProcessId;
        return this;
    }

    public long getElementInstanceKey() {
        return elementInstanceKey;
    }

    public DecisionEvaluation setElementInstanceKey(final long elementInstanceKey) {
        this.elementInstanceKey = elementInstanceKey;
        return this;
    }

    public String getElementId() {
        return elementId;
    }

    public DecisionEvaluation setElementId(final String elementId) {
        this.elementId = elementId;
        return this;
    }

    public String getDecisionId() {
        return decisionId;
    }

    public DecisionEvaluation setDecisionId(final String decisionId) {
        this.decisionId = decisionId;
        return this;
    }

    public String getDecisionName() {
        return decisionName;
    }

    public DecisionEvaluation setDecisionName(final String decisionName) {
        this.decisionName = decisionName;
        return this;
    }

    public int getDecisionVersion() {
        return decisionVersion;
    }

    public DecisionEvaluation setDecisionVersion(final int decisionVersion) {
        this.decisionVersion = decisionVersion;
        return this;
    }

    public String getRootDecisionName() {
        return rootDecisionName;
    }

    public DecisionEvaluation setRootDecisionName(final String rootDecisionName) {
        this.rootDecisionName = rootDecisionName;
        return this;
    }

    public String getRootDecisionId() {
        return rootDecisionId;
    }

    public DecisionEvaluation setRootDecisionId(final String rootDecisionId) {
        this.rootDecisionId = rootDecisionId;
        return this;
    }

    public String getRootDecisionDefinitionId() {
        return rootDecisionDefinitionId;
    }

    public DecisionEvaluation setRootDecisionDefinitionId(final String rootDecisionDefinitionId) {
        this.rootDecisionDefinitionId = rootDecisionDefinitionId;
        return this;
    }

    public DecisionType getDecisionType() {
        return decisionType;
    }

    public DecisionEvaluation setDecisionType(final DecisionType decisionType) {
        this.decisionType = decisionType;
        return this;
    }

    public String getResult() {
        return result;
    }

    public DecisionEvaluation setResult(final String result) {
        this.result = result;
        return this;
    }

    public List<DecisionEvaluationInput> getEvaluatedInputs() {
        return evaluatedInputs;
    }

    public DecisionEvaluation setEvaluatedInputs(
            final List<DecisionEvaluationInput> evaluatedInputs) {
        this.evaluatedInputs = evaluatedInputs;
        return this;
    }

    public List<DecisionEvaluationOutput> getEvaluatedOutputs() {
        return evaluatedOutputs;
    }

    public DecisionEvaluation setEvaluatedOutputs(
            final List<DecisionEvaluationOutput> evaluatedOutputs) {
        this.evaluatedOutputs = evaluatedOutputs;
        return this;
    }

    public Object[] getSortValues() {
        return sortValues;
    }

    public DecisionEvaluation setSortValues(final Object[] sortValues) {
        this.sortValues = sortValues;
        return this;
    }


}
