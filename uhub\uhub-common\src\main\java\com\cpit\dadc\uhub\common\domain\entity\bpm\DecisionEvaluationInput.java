package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@TableName("act_decision_evaluation_input")
public class DecisionEvaluationInput extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    String id;

    String inputId;

    String inputName;

    @TableField(value = "value_")
    String value;

    String evaluatedDecisionId;

    public String getId() {
        return id;
    }

    public DecisionEvaluationInput setId(String id) {
        this.id = id;
        return this;
    }

    public String getInputId() {
        return inputId;
    }

    public DecisionEvaluationInput setInputId(String inputId) {
        this.inputId = inputId;
        return this;
    }

    public String getInputName() {
        return inputName;
    }

    public DecisionEvaluationInput setInputName(String inputName) {
        this.inputName = inputName;
        return this;
    }

    public String getValue() {
        return value;
    }

    public DecisionEvaluationInput setValue(String value) {
        this.value = value;
        return this;
    }

    public String getEvaluatedDecisionId() {
        return evaluatedDecisionId;
    }

    public DecisionEvaluationInput setEvaluatedDecisionId(String evaluatedDecisionId) {
        this.evaluatedDecisionId = evaluatedDecisionId;
        return this;
    }

    @Override
    public String getTenantId() {
        return super.getTenantId();
    }

    @Override
    public void setTenantId(String tenantId) {
        super.setTenantId(tenantId);
    }

    // 添加一个方法来设置 tenantId
    public DecisionEvaluationInput withTenantId(String tenantId) {
        this.setTenantId(tenantId);
        return this;
    }
}
