package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@TableName("act_decision_evaluation_output")
public class DecisionEvaluationOutput extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    private String outputId;
    private String outputName;
    @TableField(value = "value_")
    private String value;
    private String evaluatedDecisionId;
    private String ruleId;
    private Integer ruleIndex;

    // 手动添加链式调用方法
    public DecisionEvaluationOutput setId(String id) {
        this.id = id;
        return this;
    }

    public DecisionEvaluationOutput setOutputId(String outputId) {
        this.outputId = outputId;
        return this;
    }

    public DecisionEvaluationOutput setOutputName(String outputName) {
        this.outputName = outputName;
        return this;
    }

    public DecisionEvaluationOutput setValue(String value) {
        this.value = value;
        return this;
    }

    public DecisionEvaluationOutput setEvaluatedDecisionId(String evaluatedDecisionId) {
        this.evaluatedDecisionId = evaluatedDecisionId;
        return this;
    }

    public DecisionEvaluationOutput setRuleId(String ruleId) {
        this.ruleId = ruleId;
        return this;
    }

    public DecisionEvaluationOutput setRuleIndex(Integer ruleIndex) {
        this.ruleIndex = ruleIndex;
        return this;
    }

    // 添加一个方法来设置 tenantId
    public DecisionEvaluationOutput withTenantId(String tenantId) {
        this.setTenantId(tenantId);
        return this;
    }
}
