package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_decision_requirements")
public class DecisionRequirements extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    private Long decisionRequirementsKey;
    private String decisionRequirementsId;
    private String decisionRequirementsName;
    private int version;
    private String namespace;
    private String dmnXML;
    private String resourceName;
    private String checksum;

}
