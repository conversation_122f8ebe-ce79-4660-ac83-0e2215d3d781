package com.cpit.dadc.uhub.common.domain.entity.bpm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum DecisionType {
    DECISION_TABLE,
    LITERAL_EXPRESSION,

    UNSPECIFIED,
    UNKNOWN;

    private static final Logger LOGGER = LoggerFactory.getLogger(FlowNodeType.class);

    public static DecisionType fromZeebeDecisionType(String decisionType) {
        if (decisionType == null) {
            return UNSPECIFIED;
        }
        try {
            return DecisionType.valueOf(decisionType);
        } catch (IllegalArgumentException ex) {
            LOGGER.error(
                    "Decision type not found for value [{}]. UNKNOWN type will be assigned.", decisionType);
            return UNKNOWN;
        }
    }
}
