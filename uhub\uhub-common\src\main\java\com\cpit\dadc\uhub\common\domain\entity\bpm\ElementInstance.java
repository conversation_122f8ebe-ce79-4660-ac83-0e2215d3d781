package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_element_instance")
public class ElementInstance extends BaseEntity {

    @TableId
    @TableField(value = "key_")
    private Long key;

    private Long position;

    private String elementId;

    private BpmnElementType bpmnElementType;

    private Long processInstanceKey;

    private Long processDefinitionKey;

    private Long scopeKey;

    private ElementInstanceState state = ElementInstanceState.ACTIVATING;

    private Long startTime;

    private Long endTime;

    public ElementInstance(Long key, Long position, String elementId, BpmnElementType bpmnElementType, Long processInstanceKey, long processDefinitionKey, Long scopeKey) {
        this.key = key;
        this.elementId = elementId;
        this.position = position;
        this.bpmnElementType = bpmnElementType;
        this.processInstanceKey = processInstanceKey;
        this.processDefinitionKey = processDefinitionKey;
        this.scopeKey = scopeKey;
    }
}