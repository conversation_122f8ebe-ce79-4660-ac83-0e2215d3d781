package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("act_element_instance_state_transition")
public class ElementInstanceStateTransition {

    @TableId
    @TableField(value = "id")
    private String partitionIdWithPosition;

    private Long elementInstanceKey;

    private ElementInstanceState state;

    private Long timestamp;
}
