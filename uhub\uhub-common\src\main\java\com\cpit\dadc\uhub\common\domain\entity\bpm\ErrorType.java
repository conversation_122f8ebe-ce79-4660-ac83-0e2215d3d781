package com.cpit.dadc.uhub.common.domain.entity.bpm;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum ErrorType {
    UNSPECIFIED("Unspecified"),
    UNKNOWN("Unknown"),
    IO_MAPPING_ERROR("I/O mapping error"),
    JOB_NO_RETRIES("No more retries left"),
    CONDITION_ERROR("Condition error"),
    EXTRACT_VALUE_ERROR("Extract value error"),
    CALLED_ELEMENT_ERROR("Called element error"),
    UNHANDLED_ERROR_EVENT("Unhandled error event"),
    MESSAGE_SIZE_EXCEEDED("Message size exceeded"),
    CALLED_DECISION_ERROR("Called decision error"),
    DECISION_EVALUATION_ERROR("Decision evaluation error"),

    FORM_NOT_FOUND("Form not found");

    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorType.class);

    private String title;

    ErrorType(String title) {
        this.title = title;
    }

    public static ErrorType fromZeebeErrorType(String errorType) {
        if (errorType == null) {
            return UNSPECIFIED;
        }
        try {
            return ErrorType.valueOf(errorType);
        } catch (IllegalArgumentException ex) {
            LOGGER.error(
                    "Error type not found for value [{}]. UNKNOWN type will be assigned.", errorType);
            return UNKNOWN;
        }
    }

    public String getTitle() {
        return title;
    }
}
