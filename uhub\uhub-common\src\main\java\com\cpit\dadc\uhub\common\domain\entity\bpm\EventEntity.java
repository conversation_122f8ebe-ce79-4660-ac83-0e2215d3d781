package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_event")
public class EventEntity  extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private String eventId;

    private long key;

    private int partitionId;

    /** Process data. */
    private Long processDefinitionKey;

    private Long processInstanceKey;
    private String bpmnProcessId;

    /** Activity data. */
    private String flowNodeId;

    private Long flowNodeInstanceKey;

    /** Event data. */
    private EventSourceType eventSourceType;

    private EventType eventType;
    private LocalDateTime dateTime;

    /** Metadata */
    @TableField(exist = false)
    private EventMetadataEntity metadata;

    private String tenantId ;

    private Long position;
    private Long positionIncident;
    private Long positionProcessMessageSubscription;
    private Long positionJob;

    public String getEventId() {
        return eventId;
    }

    public EventEntity setEventId(String eventId) {
        this.eventId = eventId;
        return this;
    }


    public long getKey() {
        return key;
    }

    public EventEntity setKey(long key) {
        this.key = key;
        return (EventEntity) this;
    }

    public int getPartitionId() {
        return partitionId;
    }

    public EventEntity setPartitionId(int partitionId) {
        this.partitionId = partitionId;
        return (EventEntity) this;
    }

    public Long getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public EventEntity setProcessDefinitionKey(final Long processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
        return this;
    }

    public Long getProcessInstanceKey() {
        return processInstanceKey;
    }

    public EventEntity setProcessInstanceKey(final Long processInstanceKey) {
        this.processInstanceKey = processInstanceKey;
        return this;
    }

    public String getBpmnProcessId() {
        return bpmnProcessId;
    }

    public EventEntity setBpmnProcessId(final String bpmnProcessId) {
        this.bpmnProcessId = bpmnProcessId;
        return this;
    }

    public String getFlowNodeId() {
        return flowNodeId;
    }

    public EventEntity setFlowNodeId(final String flowNodeId) {
        this.flowNodeId = flowNodeId;
        return this;
    }

    public Long getFlowNodeInstanceKey() {
        return flowNodeInstanceKey;
    }

    public EventEntity setFlowNodeInstanceKey(final Long flowNodeInstanceKey) {
        this.flowNodeInstanceKey = flowNodeInstanceKey;
        return this;
    }

    public EventSourceType getEventSourceType() {
        return eventSourceType;
    }

    public EventEntity setEventSourceType(final EventSourceType eventSourceType) {
        this.eventSourceType = eventSourceType;
        return this;
    }

    public EventType getEventType() {
        return eventType;
    }

    public EventEntity setEventType(final EventType eventType) {
        this.eventType = eventType;
        return this;
    }

    public LocalDateTime getDateTime() {
        return dateTime;
    }

    public EventEntity setDateTime(final LocalDateTime dateTime) {
        this.dateTime = dateTime;
        return this;
    }

    public EventMetadataEntity getMetadata() {
        return metadata;
    }

    public EventEntity setMetadata(final EventMetadataEntity metadata) {
        this.metadata = metadata;
        return this;
    }

    @Override
    public String getTenantId() {
        return super.getTenantId();
    }

    @Override
    public void setTenantId(String tenantId) {
        super.setTenantId(tenantId);
    }

    public Long getPosition() {
        return position;
    }

    public EventEntity setPosition(final Long position) {
        this.position = position;
        return this;
    }

    public Long getPositionIncident() {
        return positionIncident;
    }

    public EventEntity setPositionIncident(final Long positionIncident) {
        this.positionIncident = positionIncident;
        return this;
    }

    public Long getPositionProcessMessageSubscription() {
        return positionProcessMessageSubscription;
    }

    public EventEntity setPositionProcessMessageSubscription(
            final Long positionProcessMessageSubscription) {
        this.positionProcessMessageSubscription = positionProcessMessageSubscription;
        return this;
    }

    public Long getPositionJob() {
        return positionJob;
    }

    public EventEntity setPositionJob(final Long positionJob) {
        this.positionJob = positionJob;
        return this;
    }


}
