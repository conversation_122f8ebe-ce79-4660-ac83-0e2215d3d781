package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;


@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_event_meta_data")
public class EventMetadataEntity  extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private String eventId;
    /**
     * Job data.
     */
    private String jobType;

    private Integer jobRetries;
    private String jobWorker;
    private LocalDateTime jobDeadline;

    private String jobCustomHeadersJson;

    private Long jobKey;

    /**
     * Incident data.
     */
    private ErrorType incidentErrorType;

    private String incidentErrorMessage;

    /**
     * Message data.
     */
    private String messageName;

    private String correlationKey;

    public String getEventId() {
        return eventId;
    }

    public EventMetadataEntity setEventId(final String eventId) {
        this.eventId = eventId;
        return this;
    }

    public String getJobType() {
        return jobType;
    }

    public EventMetadataEntity setJobType(final String jobType) {
        this.jobType = jobType;
        return this;
    }

    public Integer getJobRetries() {
        return jobRetries;
    }

    public EventMetadataEntity setJobRetries(final Integer jobRetries) {
        this.jobRetries = jobRetries;
        return this;
    }

    public String getJobWorker() {
        return jobWorker;
    }

    public EventMetadataEntity setJobWorker(final String jobWorker) {
        this.jobWorker = jobWorker;
        return this;
    }

    public LocalDateTime getJobDeadline() {
        return jobDeadline;
    }

    public EventMetadataEntity setJobDeadline(final LocalDateTime jobDeadline) {
        this.jobDeadline = jobDeadline;
        return this;
    }

/*    public Map<String, String> getJobCustomHeaders() {
        return jobCustomHeaders;
    }

    public EventMetadataEntity setJobCustomHeaders(final Map<String, String> jobCustomHeaders) {
        this.jobCustomHeaders = jobCustomHeaders;
        return this;
    }*/

    public Map<String, String> getJobCustomHeaders() {
        if (jobCustomHeadersJson == null) {
            return null;
        }
        try {
            return new ObjectMapper().readValue(jobCustomHeadersJson, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing JSON", e);
        }
    }

    public void setJobCustomHeaders(Map<String, String> jobCustomHeaders) {
        if (jobCustomHeaders == null) {
            this.jobCustomHeadersJson = null;
        } else {
            try {
                this.jobCustomHeadersJson = new ObjectMapper().writeValueAsString(jobCustomHeaders);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("Error converting map to JSON", e);
            }
        }
    }

    public Long getJobKey() {
        return jobKey;
    }

    public void setJobKey(final Long jobKey) {
        this.jobKey = jobKey;
    }

    public ErrorType getIncidentErrorType() {
        return incidentErrorType;
    }

    public void setIncidentErrorType(final ErrorType incidentErrorType) {
        this.incidentErrorType = incidentErrorType;
    }

    public String getIncidentErrorMessage() {
        return incidentErrorMessage;
    }

    public void setIncidentErrorMessage(final String incidentErrorMessage) {
        this.incidentErrorMessage = incidentErrorMessage;
    }

    public String getMessageName() {
        return messageName;
    }

    public EventMetadataEntity setMessageName(final String messageName) {
        this.messageName = messageName;
        return this;
    }

    public String getCorrelationKey() {
        return correlationKey;
    }

    public EventMetadataEntity setCorrelationKey(final String correlationKey) {
        this.correlationKey = correlationKey;
        return this;
    }


}