package com.cpit.dadc.uhub.common.domain.entity.bpm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum EventSourceType {
    JOB,
    PROCESS_INSTANCE,
    INCIDENT,
    PROCESS_MESSAGE_SUBSCRIPTION,
    UNKNOWN,
    UNSPECIFIED;

    private static final Logger LOGGER = LoggerFactory.getLogger(EventSourceType.class);

    public static EventSourceType fromZeebeValueType(String valueType) {
        if (valueType == null) {
            return UNSPECIFIED;
        }
        try {
            return EventSourceType.valueOf(valueType);
        } catch (IllegalArgumentException ex) {
            LOGGER.error(
                    "Value type not found for value [{}]. UNKNOWN type will be assigned.", valueType);
            return UNKNOWN;
        }
    }
}
