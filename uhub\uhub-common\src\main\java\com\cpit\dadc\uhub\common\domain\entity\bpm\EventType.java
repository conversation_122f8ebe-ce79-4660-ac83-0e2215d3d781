package com.cpit.dadc.uhub.common.domain.entity.bpm;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum EventType {
    CREATED,

    RESOLVED,

    SEQUENCE_FLOW_TAKEN,

    ELEMENT_ACTIVATING,
    <PERSON>LEMENT_ACTIVATED,
    <PERSON>LEMENT_COMPLETING,
    <PERSON>LEMENT_COMPLETED,
    ELEMENT_TERMINATED,

    // JOB
    ACTIVATED,

    COMPLETED,

    TIMED_OUT,

    FAILED,

    RETRIES_UPDATED,

    // MESSAGE
    CORRELATED,

    CANCELED,

    MIGRATED,
    UNKNOWN;

    private static final Logger LOGGER = LoggerFactory.getLogger(EventType.class);

    public static EventType from<PERSON><PERSON>beIntent(String intent) {
        try {
            return EventType.valueOf(intent);
        } catch (IllegalArgumentException ex) {
            LOGGER.error("Event type not found for value [{}]. UNKNOWN type will be assigned.", intent);
            return UNKNOWN;
        }
    }

}