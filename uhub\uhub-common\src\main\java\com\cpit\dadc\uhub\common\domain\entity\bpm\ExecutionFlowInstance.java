package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("cpit_execution_flow_instance")
public class ExecutionFlowInstance extends BaseEntity {

    @TableId
    private String id;
    private int year;
    private int month;
    private String date;
    private long flowNodeInstanceId;
    private String flowNodeId;
    private String flowNodeName;
    private String inputVariable;
    private String outputVariable;
    private String parentFlowNodeId;
    private long processInstanceKey;
    private long processDefinitionKey;
    private String bpmnProcessId;
    private FlowNodeState state;
    private FlowNodeType type;
    private Long startDate;
    private Long endDate;
    private Long duration;

}