package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_flow_node_instance")
public class FlowNodeInstance extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("flow_node_instance_id")
    private Long flowNodeInstanceId;

    private String flowNodeId;

    private String parentFlowNodeId;

    private Long processInstanceKey;

    private Long processDefinitionKey;

    private String bpmnProcessId;

    private Long position;

    private FlowNodeType type;

    private Long incidentKey;

    private int partitionId;

    private String treePath;

    private boolean incident;

    @JsonIgnore
    private Object[] sortValues;

    private Long startDate;

    private Long endDate;

    private FlowNodeState state;

    @TableField("processed")
    private boolean processed = false;
//    private Long duration;
    //todo
//    private Long scopeKey;

    // 生成 getter & setter
    public boolean isProcessed() {
        return processed;
    }

    public FlowNodeInstance setProcessed(boolean processed) {
        this.processed = processed;
        return this;
    }

    public String getFlowNodeId() {
        return flowNodeId;
    }

    public FlowNodeInstance setFlowNodeId(String flowNodeId) {
        this.flowNodeId = flowNodeId;
        return this;
    }

    public Long getStartDate() {
        return startDate;
    }

    public FlowNodeInstance setStartDate(Long startDate) {
        this.startDate = startDate;
        return this;
    }

    public Long getEndDate() {
        return endDate;
    }

    public FlowNodeInstance setEndDate(Long endDate) {
        this.endDate = endDate;
        return this;
    }

    public FlowNodeState getState() {
        return state;
    }

    public FlowNodeInstance setState(FlowNodeState state) {
        this.state = state;
        return this;
    }

    public FlowNodeType getType() {
        return type;
    }

    public FlowNodeInstance setType(FlowNodeType type) {
        this.type = type;
        return this;
    }

    public Long getIncidentKey() {
        return incidentKey;
    }

    public FlowNodeInstance setIncidentKey(Long incidentKey) {
        this.incidentKey = incidentKey;
        return this;
    }

    public Long getProcessInstanceKey() {
        return processInstanceKey;
    }

    public FlowNodeInstance setProcessInstanceKey(Long processInstanceKey) {
        this.processInstanceKey = processInstanceKey;
        return this;
    }

    public Long getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public FlowNodeInstance setProcessDefinitionKey(Long processDefinitionKey) {
        this.processDefinitionKey = processDefinitionKey;
        return this;
    }

    public String getBpmnProcessId() {
        return bpmnProcessId;
    }

    public FlowNodeInstance setBpmnProcessId(String bpmnProcessId) {
        this.bpmnProcessId = bpmnProcessId;
        return this;
    }

    public String getTreePath() {
        return treePath;
    }

    public FlowNodeInstance setTreePath(String treePath) {
        this.treePath = treePath;
        return this;
    }


    public Long getPosition() {
        return position;
    }

    public FlowNodeInstance setPosition(Long position) {
        this.position = position;
        return this;
    }

    public Object[] getSortValues() {
        return sortValues;
    }

    public FlowNodeInstance setSortValues(Object[] sortValues) {
        this.sortValues = sortValues;
        return this;
    }

    public boolean isIncident() {
        return incident;
    }

    public FlowNodeInstance setIncident(final boolean incident) {
        this.incident = incident;
        return this;
    }

/*    public Long getDuration() {
        return duration;
    }

    public FlowNodeInstance setDuration(final Long duration) {
        this.duration = duration;
        return this;
    }*/


}
