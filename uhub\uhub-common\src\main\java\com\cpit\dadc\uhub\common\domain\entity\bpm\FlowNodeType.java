package com.cpit.dadc.uhub.common.domain.entity.bpm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum FlowNodeType {

    UNSPECIFIED,
    PROCESS,
    SUB_PROCESS,
    EVENT_SUB_PROCESS,
    START_EVENT,
    INTERMEDIATE_CATCH_EVENT,
    BOUNDARY_EVENT,
    END_EVENT,
    SERVICE_TASK,
    USER_TASK,
    RECEIVE_TASK,
    EXCLUSIVE_GATEWAY,
    PARALLEL_GATEWAY,
    EVENT_BASED_GATEWAY,
    SEQUENCE_FLOW,
    MULTI_INSTANCE_BODY,
    CALL_ACTIVITY,
    UNKNOWN;

    private static final Logger LOGGER = LoggerFactory.getLogger(FlowNodeType.class);

    private FlowNodeType() {
    }

    public static FlowNodeType fromZeebeBpmnElementType(String bpmnElementType) {
        if (bpmnElementType == null) {
            return UNSPECIFIED;
        } else {
            try {
                return valueOf(bpmnElementType);
            } catch (IllegalArgumentException var2) {
                LOGGER.error("Flow node type not found for value [{}]. UNKNOWN type will be assigned.", bpmnElementType);
                return UNKNOWN;
            }
        }
    }
}
