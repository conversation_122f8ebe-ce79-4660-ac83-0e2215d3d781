package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_form")
public class Form extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    private String bpmnId;
    private Long processDefinitionId;
    private String schema;
    private Long version;
    private Boolean embedded;
    private Boolean isDeleted;

    public Form(Long processDefinitionId, String bpmnId, String schema, Long version, String tenantId, String formKey, Boolean embedded, Boolean isDeleted) {
        if (embedded) {
            this.setId(createId(processDefinitionId, bpmnId));
        } else {
            this.setId(formKey);
        }

        this.bpmnId = bpmnId;
        this.processDefinitionId = processDefinitionId;
        this.schema = schema;
        this.version = version;
        super.setTenantId(tenantId);
        this.embedded = embedded;
        this.isDeleted = isDeleted;
    }

    public Form(Long processDefinitionId, String bpmnId, String schema) {
        this(processDefinitionId, bpmnId, schema, "<default>");
    }

    public Form(Long processDefinitionId, String bpmnId, String schema, String tenantId) {
        this.setId(createId(processDefinitionId, bpmnId));
        this.setTenantId(tenantId);
        this.bpmnId = bpmnId;
        this.processDefinitionId = processDefinitionId;
        this.schema = schema;
        super.setTenantId(tenantId);
        this.embedded = true;
        this.isDeleted = false;
    }

    public Boolean getEmbedded() {
        return this.embedded;
    }

    public Form setEmbedded(Boolean embedded) {
        this.embedded = embedded;
        return this;
    }

    public String getSchema() {
        return this.schema;
    }

    public Form setSchema(final String schema) {
        this.schema = schema;
        return this;
    }

    public String getBpmnId() {
        return this.bpmnId;
    }

    public Form setBpmnId(final String bpmnId) {
        this.bpmnId = bpmnId;
        return this;
    }

    public Long getProcessDefinitionId() {
        return this.processDefinitionId;
    }

    public Form setProcessDefinitionId(final Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
        return this;
    }

    public Long getVersion() {
        return this.version;
    }

    public Form setVersion(Long version) {
        this.version = version;
        return this;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public Form setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
        return this;
    }

    public static String createId(Long processId, String formKey) {
        return String.format("%s_%s", processId, formKey);
    }

    public int hashCode() {
        return Objects.hash(new Object[]{super.hashCode(), this.bpmnId, this.processDefinitionId, this.schema, this.version});
    }

}