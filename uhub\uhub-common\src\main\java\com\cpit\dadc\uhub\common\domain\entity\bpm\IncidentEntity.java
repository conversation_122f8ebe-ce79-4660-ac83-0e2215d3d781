package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_incident")
public class IncidentEntity extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private long key;

    private ErrorType errorType;

    private String errorMessage;

    // Is only used by binding to ES results
    private Integer errorMessageHash;

    private IncidentState state;

    private String flowNodeId;

    private Long flowNodeInstanceKey;

    private Long jobKey;

    private Long processInstanceKey;

    private Long creationTime;

    private Long processDefinitionKey;

    private String bpmnProcessId;

    private String treePath;

    private int partitionId;

    private Long position;

    @Deprecated @JsonIgnore
    private boolean pending ;

    public IncidentEntity setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        setErrorMessageHash(errorMessage.hashCode());
        return this;
    }

    public Integer getErrorMessageHash() {
        return errorMessage.hashCode();
    }

    public IncidentEntity setErrorMessageHash(Integer errorMessageHash) {
        this.errorMessageHash = errorMessageHash;
        return this;
    }

}
