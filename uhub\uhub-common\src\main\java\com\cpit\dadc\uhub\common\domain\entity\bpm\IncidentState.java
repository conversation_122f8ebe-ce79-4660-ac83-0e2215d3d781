package com.cpit.dadc.uhub.common.domain.entity.bpm;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public enum IncidentState {
    ACTIVE("CREATED"),
    MIGRATED("MIGRATED"),
    RESOLVED("RESOLVED"),
    PENDING(null);

    private static Map<String, IncidentState> intentMap = new HashMap<>();

    static {
        Arrays.stream(IncidentState.values()).forEach(is -> intentMap.put(is.getIncidentIntent(), is));
    }

    private String incidentIntent;

    IncidentState(String incidentIntent) {
        this.incidentIntent = incidentIntent;
    }

    public static IncidentState createFrom(String incidentIntent) {
        return intentMap.get(incidentIntent);
    }

    public String getIncidentIntent() {
        return incidentIntent;
    }
}
