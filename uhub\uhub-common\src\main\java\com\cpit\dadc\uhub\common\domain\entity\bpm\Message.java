package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_message")
public class Message extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long key;


    private Long position;


    private String name;


    private String correlationKey;


    private String messageId;


    private Long timeToLive;


    private MessageState state = MessageState.PUBLISHED;


    private Long timestamp = -1L;
}
