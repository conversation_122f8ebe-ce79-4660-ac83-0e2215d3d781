package com.cpit.dadc.uhub.common.domain.entity.bpm;



import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data

@TableName("act_message_correlation")
public class MessageCorrelation extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @TableField(value = "partitionIdWithPosition")
    private String partitionIdWithPosition;

    
    private Long messageKey;

    
    private String messageName;

    
    private Long timestamp;

    
    private Long processInstanceKey;

    
    private Long elementInstanceKey;

    
    private String elementId;

    
    private Long processDefinitionKey;
}
