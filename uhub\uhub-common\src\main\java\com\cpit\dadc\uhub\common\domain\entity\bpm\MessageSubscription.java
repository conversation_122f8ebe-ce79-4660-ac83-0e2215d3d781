package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("message_subscription")
public class MessageSubscription extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @TableField(value = "key_")
    private Long key;
    
    private Long position;

    private String messageName;
    
    private String messageCorrelationKey;

    private Long processInstanceKey;

    private Long elementInstanceKey;

    private Long processDefinitionKey;
    
    private String elementId;

    private MessageSubscriptionState state = MessageSubscriptionState.CREATED;

    private Long timestamp = -1L;
}
