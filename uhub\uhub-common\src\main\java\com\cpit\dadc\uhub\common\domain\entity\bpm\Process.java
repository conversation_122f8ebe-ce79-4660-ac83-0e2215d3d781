package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.*;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_process")
public class Process {

    @TableId(value = "id")
    @TableField(value = "id")
    private String id;

    private Long ProcessDefinitionKey;

    private String bpmnProcessId;
    private Integer version;

    private String bpmnXml;

    private LocalDateTime deployTime;

    private String resourceName;

    private String name;

    private String checksum;

    private boolean startedByForm;
    private String formKey;
    private String formId;
    private Boolean isFormEmbedded;

    @TableField(exist = false)
    private List<ProcessFlowNode> flowNodes = new ArrayList<>();

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String tenantId;
}
