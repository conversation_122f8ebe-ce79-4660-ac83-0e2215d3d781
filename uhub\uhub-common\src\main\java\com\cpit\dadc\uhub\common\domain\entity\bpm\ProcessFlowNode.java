package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_process_flow_node")
public class ProcessFlowNode extends BaseEntity {


    @TableId(type = IdType.ASSIGN_ID)
    @TableField(value = "id")
    private String id;

    private String flowNodeId;

    private String name;

    private Long ProcessDefinitionKey;

    public ProcessFlowNode(String flowNodeId, String name) {
        this.flowNodeId = flowNodeId;
        this.name = name;
    }

    public ProcessFlowNode(String flowNodeId, String name, String tenantId) {
        super.setTenantId(tenantId);
        this.flowNodeId = flowNodeId;
        this.name = name;
    }

    public String getId() {
        return this.id;
    }

    public ProcessFlowNode setId(String id) {
        this.id = id;
        return this;
    }

    public String getFlowNodeId() {
        return this.flowNodeId;
    }

    public ProcessFlowNode setFlowNodeId(String flowNodeId) {
        this.flowNodeId = flowNodeId;
        return this;
    }

    public String getName() {
        return this.name;
    }

    public ProcessFlowNode setName(String name) {
        this.name = name;
        return this;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            ProcessFlowNode that = (ProcessFlowNode)o;
            return Objects.equals(this.id, that.id) && Objects.equals(this.name, that.name);
        } else {
            return false;
        }
    }

    public int hashCode() {
        return Objects.hash(new Object[]{this.id, this.name});
    }
}
