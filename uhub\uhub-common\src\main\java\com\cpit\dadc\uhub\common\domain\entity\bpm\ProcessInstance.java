package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_process_instance")
public class ProcessInstance extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private Long key;

    private Long processInstanceKey;

    private String bpmnProcessId;

    private ProcessInstanceState state;

    private Long processDefinitionKey;

    private Long parentProcessInstanceKey;

    private Long parentElementInstanceKey;

    private Integer version;

    private Long position;

    private Integer partitionId;

    private Long startDate;

    private Long endDate;

}
