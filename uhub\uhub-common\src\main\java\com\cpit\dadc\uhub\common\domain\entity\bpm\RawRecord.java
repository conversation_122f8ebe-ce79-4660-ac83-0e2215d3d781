package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("raw_record")
public class RawRecord extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private long processDefinitionKey;
    private long processInstanceKey;
    private int partitionId;
    private String intent;
    private String recordType;
    private String rejectionType;
    private String rejectionReason;
    private String brokerVersion;
    private String valueType;
    private long key;
    private long position;
    private long timestamp;
    private long sourceRecordPosition;
    private int recordVersion;
    private String valueJson;
    private String authorizationsJson;
    private long operationReference;
}