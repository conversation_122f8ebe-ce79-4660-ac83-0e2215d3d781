package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;


@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_sequence_flow")
public class SequenceFlow extends BaseEntity {

  @TableId(value = "id", type = IdType.ASSIGN_ID)
  @TableField(value = "id")
  private String id;

  private Long processInstanceKey;

  private Long processDefinitionKey;

  private String bpmnProcessId;

  private String activityId;

  public String getId() {
    return id;
  }

  public SequenceFlow setId(String id) {
    this.id = id;
    return this;
  }

  public Long getProcessInstanceKey() {
    return processInstanceKey;
  }

  public SequenceFlow setProcessInstanceKey(Long processInstanceKey) {
    this.processInstanceKey = processInstanceKey;
    return this;
  }

  public Long getProcessDefinitionKey() {
    return processDefinitionKey;
  }

  public SequenceFlow setProcessDefinitionKey(Long processDefinitionKey) {
    this.processDefinitionKey = processDefinitionKey;
    return this;
  }

  public String getBpmnProcessId() {
    return bpmnProcessId;
  }

  public SequenceFlow setBpmnProcessId(String bpmnProcessId) {
    this.bpmnProcessId = bpmnProcessId;
    return this;
  }

  public String getActivityId() {
    return activityId;
  }

  public SequenceFlow setActivityId(String activityId) {
    this.activityId = activityId;
    return this;
  }

  @Override
  public String getTenantId() {
    return super.getTenantId();
  }

  @Override
  public void setTenantId(String tenantId) {
    super.setTenantId(tenantId);
  }

}
