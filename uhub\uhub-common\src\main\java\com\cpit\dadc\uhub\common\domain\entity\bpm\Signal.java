package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("act_signal")
public class Signal extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @TableField(value = "key_")
    private Long key;

    
    private Long position;

    
    private String name;

    
    private Long timestamp;
}
