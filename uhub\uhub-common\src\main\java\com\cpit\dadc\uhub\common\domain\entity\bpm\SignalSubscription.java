package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_signal_subscription")
public class SignalSubscription extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @TableField(value = "key_")
    private Long key;

    private Long position;

    private String signalName;

    private Long processDefinitionKey;

    private String elementId;

    private SignalSubscriptionState state = SignalSubscriptionState.CREATED;

    private Long timestamp;

    public SignalSubscription(long key, long position, String signalName, long processDefinitionKey, String catchEventId) {
        this.key = key;
        this.position = position;
        this.signalName = signalName;
        this.processDefinitionKey = processDefinitionKey;
        this.elementId = catchEventId;
    }
}
