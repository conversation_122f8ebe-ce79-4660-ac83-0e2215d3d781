package com.cpit.dadc.uhub.common.domain.entity.bpm;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import com.cpit.dadc.uhub.common.domain.vo.task.TaskVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_task")
public class Task extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务ID，同时也是jobKey
     */
    private String taskId;

    private Long position;
    
    private int partitionId;
    
    private String jobType;

    private TaskState state ;

    private Long creationTime;
    
    private Long completionTime;
    
    private LocalDateTime dueDate;

    private Integer retries;

    private String worker;
    private Long processDefinitionKey;
    private int processDefinitionVersion;
    private Long bpmnProcessId;
    private String flowNodeBpmnId;
    private Long flowNodeInstanceId;
    private Long processInstanceKey;
    private String assignee;
    private Boolean assigned;
    private String[] candidateGroups;
    private String[] candidateUsers;
    private String formKey;
    private String formId;
    private Long formVersion;
    private Boolean isFormEmbedded;
    private LocalDateTime followUpDate;

    private TaskImplementation implementation;
//    private Long duration;
    public TaskVO toVO(Task task) {
        if (task == null) {
            return null;
        }
        TaskVO vo = new TaskVO();

        vo.setId(task.getId());
        vo.setProcessDefinitionKey(task.getProcessDefinitionKey());
        vo.setProcessDefinitionVersion(task.getProcessDefinitionVersion());
        vo.setBpmnProcessId(task.getBpmnProcessId());
        vo.setJobType(task.getJobType());
        vo.setState(task.getState());
        vo.setCreationTime(task.getCreationTime());
        vo.setCompletionTime(task.getCompletionTime());
        vo.setDueDate(task.getDueDate());
        vo.setRetries(task.getRetries());
        vo.setWorker(task.getWorker());
        vo.setFlowNodeBpmnId(task.getFlowNodeBpmnId());
        vo.setFlowNodeInstanceId(task.getFlowNodeInstanceId());
        vo.setProcessInstanceKey(task.getProcessInstanceKey());
        vo.setAssignee(task.getAssignee());
        vo.setAssigned(task.getAssigned());
        vo.setCandidateGroups(task.getCandidateGroups());
        vo.setCandidateUsers(task.getCandidateUsers());
        vo.setFormKey(task.getFormKey());
        vo.setFormId(task.getFormId());
        vo.setFormVersion(task.getFormVersion());
        vo.setIsFormEmbedded(task.getIsFormEmbedded());
        vo.setFollowUpDate(task.getFollowUpDate());
        vo.setImplementation(task.getImplementation());
        vo.setTenantId(task.getTenantId());
//        vo.setDuration(task.getDuration());
        return vo;
    }
}
