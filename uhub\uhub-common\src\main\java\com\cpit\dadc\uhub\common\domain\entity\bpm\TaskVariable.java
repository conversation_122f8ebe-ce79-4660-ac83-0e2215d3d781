package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_task_variable")
public class TaskVariable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private long key;
    private String taskId;
    private String name;
    private String value;
    private String fullValue;
    private boolean isPreview;
    private int partitionId;
    private String tenantId;

    public static String getIdBy(String taskId, String name) {
        return String.format("%s-%s", taskId, name);
    }

    public boolean getIsPreview() {
        return isPreview;
    }

    public void setIsPreview(final boolean preview) {
        isPreview = preview;
    }

    public static TaskVariable createFrom(String taskId, Variable variable) {
        return TaskVariable.builder()
                .id(getIdBy(taskId, variable.getName()))
                .taskId(taskId)
                .name(variable.getName())
                .value(variable.getValue())
                .isPreview(variable.isPreview())
                .fullValue(variable.getFullValue())
                .tenantId(variable.getTenantId())
                .build();
    }

    public static TaskVariable createFrom(
            String tenantId, String taskId, String name, String value, int variableSizeThreshold) {
        TaskVariable entity = new TaskVariable();
        entity.setId(getIdBy(taskId, name));
        entity.setTaskId(taskId);
        entity.setName(name);
        if (value.length() > variableSizeThreshold) {
            entity.setValue(value.substring(0, variableSizeThreshold));
            entity.setIsPreview(true);
        } else {
            entity.setValue(value);
        }
        entity.setFullValue(value);
        entity.setTenantId(tenantId);
        return entity;
    }
}
