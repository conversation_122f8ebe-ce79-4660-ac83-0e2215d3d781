package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data

@TableName("act_timer")
public class Timer extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private Long timeKey;

    private Long position;

    private Long dueDate;

    private int repetitions;

    String targetElementId;

    private Long processInstanceKey;

    private Long elementInstanceKey;

    private Long processDefinitionKey;

    private TimerState state;

}
