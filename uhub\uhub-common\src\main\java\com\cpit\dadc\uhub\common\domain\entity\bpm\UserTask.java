package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_user_task")
public class UserTask extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @TableField(value = "key_")
    private Long key;

    
    private Long position;

    
    private Long processInstanceKey;

    
    private Long processDefinitionKey;

    
    private Long elementInstanceKey;

    
    private String assignee;

    
    private String candidateGroups;

    
    private String formKey;

    private UserTaskState state = UserTaskState.CREATED;

    
    private Long timestamp;

    
    private Long startTime;

    
    private Long endTime;
}
