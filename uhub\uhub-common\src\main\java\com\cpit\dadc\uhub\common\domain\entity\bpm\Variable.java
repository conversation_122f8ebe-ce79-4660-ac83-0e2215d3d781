package com.cpit.dadc.uhub.common.domain.entity.bpm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("act_variable")
public class Variable extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private Long key;

    private String name;

    private String value;

    private String fullValue;

    private String scopeFlowNodeId;

    private Long scopeKey;

    private Long processDefinitionKey;

    private String bpmnProcessId;

    @TableField("process_instance_key")
    private String processInstanceKey;

    private boolean isPreview;

    private Long timestamp;

    private Long position;
}
