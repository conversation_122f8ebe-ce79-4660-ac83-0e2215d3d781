package com.cpit.dadc.uhub.common.domain.entity.connectors;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description:
 * @date: 2024-08-12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("cpit_element_template")
public class ElementTemplate {
    /** 模板id */
    private String templateId;

    /** 连接器名字*/
    private String name;

    /** 模板json */
    private String json;

    /** 创建用户id*/
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 更新用户id*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 创建时间*/
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间*/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
