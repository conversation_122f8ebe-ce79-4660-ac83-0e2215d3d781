package com.cpit.dadc.uhub.common.domain.entity.definition;

import com.baomidou.mybatisplus.annotation.*;
import com.cpit.dadc.uhub.common.domain.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 流程设计表
 * @date: 2022-06-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("cpit_process_design")
public class ProcessDesign extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /** 流程名称 */
    @TableField(value = "process_name")
    private String processName;

    /** 流程定义key */
    private String processKey;

    /** 业务类型 */
    private String businessType;

    /** 区分是dmn还是bpmn */
    private String type;

    /** 流程定义的xml */
    private String processXml;

    /** 流程定义的yml */
    private String processYml;

    /** 流程是否发布(0:未发布; 1:已发布) */
    private Integer processStatus;

    /** 有新版本待发布*/
    private Boolean isEdited;

    /** 租户id*/
    @TableField(fill = FieldFill.INSERT)
    private String tenantId;

    public enum ProcessStatusEnum {

        PUBLISHED(1, "已发布"),
        UNPUBLISHED(0, "未发布");

        private Integer value;
        private String description;

        private ProcessStatusEnum(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    public enum ProcessTypeEnum {

        BPMN("bpmn", "BPMN"),
        DMN("dmn", "DMN");

        private String value;
        private String description;

        private ProcessTypeEnum(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }
}
