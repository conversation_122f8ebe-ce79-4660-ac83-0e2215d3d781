package com.cpit.dadc.uhub.common.domain.vo.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description:
 * @date: 2024-08-29
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppAuthVO implements Serializable {
    @Schema(description = "id")
    private String id;

    @Schema(description = "访问秘钥")
    private String accessKey;

    @Schema(description = "秘钥")
    private String secretKey;

    @Schema(description = "盐值")
    private String salt;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "秘钥到期时间")
    private LocalDateTime expireTime;

    @Schema(description = "状态，#0:已禁用，#1:启")
    private Integer status;
}
