package com.cpit.dadc.uhub.common.domain.vo.connector;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-08-13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConnectorTemplateVO implements Serializable {
    @Schema(description = "模板id")
    private String templateId;
    @Schema(description = "模板名")
    private String name;
    @Schema(description = "模板json数据")
    private String json;
}
