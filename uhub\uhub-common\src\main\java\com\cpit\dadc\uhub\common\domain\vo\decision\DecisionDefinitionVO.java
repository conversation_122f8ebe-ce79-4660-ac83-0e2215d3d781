package com.cpit.dadc.uhub.common.domain.vo.decision;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DecisionDefinitionVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "决策键")
    private Long decisionKey;

    @Schema(description = "决策标识符")
    private String decisionId;

    @Schema(description = "决策名称")
    private String decisionName;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "决策需求key")
    private Long decisionRequirementsKey;

    @Schema(description = "决策需求id")
    private String decisionRequirementsId;

    @Schema(description = "租户")
    private String tenantId;

    @Schema(description = "发布用户")
    private String createBy;

    @Schema(description = "更新用户")
    private String updateBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
