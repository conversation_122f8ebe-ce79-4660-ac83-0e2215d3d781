package com.cpit.dadc.uhub.common.domain.vo.design;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusinessTypeVO {
    @Schema(description = "业务类型名称")
    private String label;

    @Schema(description = "业务类型值")
    private String value;
}
