package com.cpit.dadc.uhub.common.domain.vo.design;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @date: 2024-08-02
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessDesignVO implements Serializable {
    @Schema(description = "id")
    private String id;

    @Schema(description = "定义名")
    private String processName;

    @Schema(description = "流程定义key")
    private String processKey;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "区分是dmn还是bpmn")
    private String type;

    @Schema(description = "流程定义的xml")
    private String processXml;

    @Schema(description = "流程定义的yml")
    private String processYml;

    @Schema(description = "流程是否发布(0:未发布; 1:已发布)")
    private Integer processStatus;

    @Schema(description = "流程是否发布为应用(false:不可以发布; true:可以发布，如果已经发布过请调用修改发布应用的接口;)")
    private Boolean canPublishApp;

    @Schema(description = "流程是否发布为应用(false:未发布; true:已发布; )")
    private Boolean publishedApp;

    @Schema(description = "是否有新版本待发布")
    private Boolean isEdited;

    @Schema(description = "租户")
    private String tenantId;

    @Schema(description = "发布用户")
    private String createBy;

    @Schema(description = "更新用户")
    private String updateBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
