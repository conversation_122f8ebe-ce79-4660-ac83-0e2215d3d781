package com.cpit.dadc.uhub.common.domain.vo.design.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-08-23
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VueFlowNode implements Serializable {
    @Schema(description = "id")
    private String id;

    @Schema(description = "label")
    private String label;

    @Schema(description = "type，取值dot，task")
    private String type;

    @Schema(description = "坐标")
    private XYPosition position;
}
