package com.cpit.dadc.uhub.common.domain.vo.design.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @date: 2024-08-23
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VueFlowVO implements Serializable {
    @Schema(description = "节点")
    private List<VueFlowNode> nodes;

    @Schema(description = "线")
    private List<VueFlowEdge> edges;
}
