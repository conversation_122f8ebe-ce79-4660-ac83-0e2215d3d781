package com.cpit.dadc.uhub.common.domain.vo.design.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @date: 2024-08-23
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XYPosition implements Serializable {
    @Schema(description = "x")
    private int x;

    @Schema(description = "y")
    private int y;
}
