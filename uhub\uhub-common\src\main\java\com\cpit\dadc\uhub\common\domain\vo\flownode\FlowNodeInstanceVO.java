package com.cpit.dadc.uhub.common.domain.vo.flownode;

import com.cpit.dadc.uhub.common.domain.entity.bpm.FlowNodeState;
import com.cpit.dadc.uhub.common.domain.entity.bpm.FlowNodeType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description:
 * @date: 2024-12-30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FlowNodeInstanceVO implements Serializable {
    @Schema(description = "节点实例id")
    private Long flowNodeInstanceId;
    @Schema(description = "节点id")
    private String flowNodeId;
    @Schema(description = "父节点实例id或流程实例id")
    private String parentFlowNodeId;
    @Schema(description = "父节点id")
    private Long processInstanceKey;
    @Schema(description = "流程定义key")
    private Long processDefinitionKey;
    @Schema(description = "bpmnProcessId")
    private String bpmnProcessId;
    @Schema(description = "偏移，可以标记同一流程实例内的节点记录导出先后顺序，该字段返回开始状态的偏移")
    private Long position;
    @Schema(description = "是否异常")
    private FlowNodeType type;
    @Schema(description = "是否异常")
    private boolean incident;
    @Schema(description = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:sss z")
    private LocalDateTime startDate;
    @Schema(description = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:sss z")
    private LocalDateTime endDate;
    @Schema(description = "耗时")
    private Long duration;
    @Schema(description = "节点状态，ACTIVE:进行中;COMPLETED:结束;TERMINATED:终端;")
    private FlowNodeState state;
}
