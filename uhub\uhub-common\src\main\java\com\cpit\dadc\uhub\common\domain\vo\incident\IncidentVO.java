package com.cpit.dadc.uhub.common.domain.vo.incident;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @description: 事件VO
 * @date: 2024-08-01
 **/
@Data
public class IncidentVO {

    @Schema(description = "主键，唯一标识符")
    private String id;

    @Schema(description = "唯一标识键，用于业务逻辑的唯一标识")
    private long key;

    @Schema(description = "错误类型，表示事故的类型（如 IO_MAPPING_ERROR）")
    private String errorType;

    @Schema(description = "错误信息，描述事故的具体原因")
    private String errorMessage;

    @Schema(description = "错误信息的哈希值，用于快速比较或索引")
    private Integer errorMessageHash;

    @Schema(description = "状态，表示事故的当前状态（如 CREATED、RESOLVED 等）")
    private String state;

    @Schema(description = "流程节点ID，表示事故发生的流程节点")
    private String flowNodeId;

    @Schema(description = "流程节点实例键，表示事故发生的流程节点实例")
    private Long flowNodeInstanceKey;

    @Schema(description = "作业键，表示与事故相关的作业")
    private Long jobKey;

    @Schema(description = "流程实例键，表示事故所属的流程实例")
    private Long processInstanceKey;

    @Schema(description = "创建时间，表示事故的创建时间（时间戳）")
    private String creationTime;

    @Schema(description = "流程定义键，表示事故所属的流程定义")
    private Long processDefinitionKey;

    @Schema(description = "BPMN流程ID，表示事故所属的BPMN流程")
    private String bpmnProcessId;

    @Schema(description = "树路径，表示事故在流程树中的路径")
    private String treePath;

    @Schema(description = "分区ID，表示事故所属的分区")
    private Integer partitionId;

    @Schema(description = "位置，表示事故在日志中的位置")
    private Long position;

    @Schema(description = "是否挂起，表示事故是否处于挂起状态")
    private Boolean pending;

    @Schema(description = "创建人，表示创建事故的用户")
    private String createBy;

    @Schema(description = "更新人，表示最后更新事故的用户")
    private String updateBy;

    @Schema(description = "创建时间，表示事故的创建时间（日期时间）")
    private Timestamp createTime;

    @Schema(description = "更新时间，表示事故的最后更新时间（日期时间）")
    private Timestamp updateTime;

    @Schema(description = "租户ID，表示事故所属的租户")
    private String tenantId;
}
