package com.cpit.dadc.uhub.common.domain.vo.process;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessVO implements Serializable {
    @Schema(description = "流程定义key")
    private long processDefinitionKey;
    @Schema(description = "bpmnProcessId")
    private String bpmnProcessId;
    @Schema(description = "流程实例key")
    private Long processInstanceKey;
    @Schema(description = "流程定义版本")
    private Integer version;
}
