package com.cpit.dadc.uhub.common.domain.vo.processinstance;

import com.cpit.dadc.uhub.common.domain.entity.bpm.ExecutionFlowInstance;
import com.cpit.dadc.uhub.common.domain.entity.bpm.FlowNodeState;
import com.cpit.dadc.uhub.common.domain.entity.bpm.FlowNodeType;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class ExecutionFlowInstanceVO {

    private String id;
    private int year;
    private int month;
    private String date;
    private String flowNodeInstanceId; // 转换为字符串
    private String flowNodeId;
    private String flowNodeName;
    private String inputVariable;
    private String outputVariable;
    private String parentFlowNodeId;
    private long processInstanceKey;
    private long processDefinitionKey;
    private String bpmnProcessId;
    private FlowNodeState state;
    private FlowNodeType type;
    private String startDate; // 转换为 yyyy-MM-dd HH:mm:ss
    private String endDate;   // 转换为 yyyy-MM-dd HH:mm:ss
    private Long duration;

    /**
     * 将 ExecutionInstance 转换为 ExecutionInstanceVO
     */
    public static ExecutionFlowInstanceVO fromEntity(ExecutionFlowInstance entity) {
        ExecutionFlowInstanceVO vo = new ExecutionFlowInstanceVO();
        vo.setId(entity.getId());
        vo.setYear(entity.getYear());
        vo.setMonth(entity.getMonth());
        vo.setDate(entity.getDate());
        vo.setFlowNodeInstanceId(String.valueOf(entity.getFlowNodeInstanceId())); // 转换为字符串
        vo.setFlowNodeId(entity.getFlowNodeId());
        vo.setFlowNodeName(entity.getFlowNodeName());
        vo.setInputVariable(entity.getInputVariable());
        vo.setOutputVariable(entity.getOutputVariable());
        vo.setParentFlowNodeId(entity.getParentFlowNodeId());
        vo.setProcessInstanceKey(entity.getProcessInstanceKey());
        vo.setProcessDefinitionKey(entity.getProcessDefinitionKey());
        vo.setBpmnProcessId(entity.getBpmnProcessId());
        vo.setState(entity.getState());
        vo.setType(entity.getType());
        vo.setStartDate(formatDate(entity.getStartDate())); // 格式化 startDate
        vo.setEndDate(formatDate(entity.getEndDate()));     // 格式化 endDate
        vo.setDuration(entity.getDuration());
        return vo;
    }

    /**
     * 将时间戳格式化为 yyyy-MM-dd HH:mm:ss
     */
    private static String formatDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(timestamp));
    }
}