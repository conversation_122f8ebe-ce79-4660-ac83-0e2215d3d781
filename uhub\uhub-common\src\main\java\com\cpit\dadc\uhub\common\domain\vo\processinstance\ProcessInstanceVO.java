package com.cpit.dadc.uhub.common.domain.vo.processinstance;

/**
 * @description: 流程实例返回实体
 * @date: 2024-10-21
 **/

import com.cpit.dadc.uhub.common.domain.entity.bpm.ProcessInstance;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProcessInstanceVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "key")
    private String key;

    @Schema(description = "流程实例key")
    private Long processInstanceKey;

    @Schema(description = "偏移位")
    private Long position;

    @Schema(description = "分区id")
    private Integer partitionId;

    @Schema(description = "流程设计bpmnProcessId")
    private String bpmnProcessId;

    @Schema(description = "流程定义版本")
    private Integer version;

    @Schema(description = "流程定义key")
    private Long processDefinitionKey;

    @Schema(description = "父流程实例key")
    private Long parentProcessInstanceKey;

    @Schema(description = "父元素实例key")
    private Long parentElementInstanceKey;

    @Schema(description = "流程实例状态")
    private String state;

    @Schema(description = "开始时间")
    private Long startDate;

    @Schema(description = "结束时间")
    private Long endDate;

    @Schema(description = "耗时")
    private Long duration;

    @Schema(description = "租户")
    private String tenantId;

    @Schema(description = "发布用户")
    private String createBy;

    @Schema(description = "更新用户")
    private String updateBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    // 自定义的时间转换为字符串的 getter 方法
    public String getStartDate() {
        return formatDateToString(startDate);
    }

    public String getEndDate() {
        return formatDateToString(endDate);
    }

    private String formatDateToString(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date(timestamp);
        return sdf.format(date);
    }

    public ProcessInstanceVO(ProcessInstance processInstance) {
        BeanUtils.copyProperties(processInstance, this);
    }
}
