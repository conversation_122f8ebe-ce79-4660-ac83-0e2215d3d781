package com.cpit.dadc.uhub.common.domain.vo.processinstance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * @description:
 * @date: 2024-12-26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProcessStartSyncVO implements Serializable {
    @Schema(description = "流程定义key")
    private long processDefinitionKey;
    @Schema(description = "bpmnProcessId")
    private String bpmnProcessId;
    @Schema(description = "流程实例key")
    private Long processInstanceKey;
    @Schema(description = "流程定义版本")
    private Integer version;
    @Schema(description = "流程实例上的变量")
    private Map<String, Object> variables;
}
