package com.cpit.dadc.uhub.common.domain.vo.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 流程执行实例VO
 * @date: 2024-08-01
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExecutionInstanceVO implements Serializable {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "日期")
    private String date;

    @Schema(description = "流程节点实例ID")
    private Long flowNodeInstanceId;

    @Schema(description = "流程节点ID")
    private String flowNodeId;

    @Schema(description = "流程节点名称")
    private String flowNodeName;

    @Schema(description = "本地输入变量")
    private String localInputVariable;

    @Schema(description = "本地输出变量")
    private String localOutputVariable;

    @Schema(description = "全局输入变量")
    private String globalInputVarchar;

    @Schema(description = "全局输出变量")
    private String globalOutputVarchar;

    @Schema(description = "父流程节点ID")
    private String parentFlowNodeId;

    @Schema(description = "流程实例Key")
    private Long processInstanceKey;

    @Schema(description = "流程定义Key")
    private Long processDefinitionKey;

    @Schema(description = "BPMN流程ID")
    private String bpmnProcessId;

    @Schema(description = "流程节点状态")
    private String state;

    @Schema(description = "流程节点类型")
    private String type;

    @Schema(description = "开始时间")
    private Long startDate;

    @Schema(description = "结束时间")
    private Long endDate;

    @Schema(description = "持续时间")
    private Long duration;
}