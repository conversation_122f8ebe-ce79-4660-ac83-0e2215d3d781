package com.cpit.dadc.uhub.common.domain.vo.statistics;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

public class ExecutionStatisticalVO<T> implements Serializable {

//    @Schema(description = "总数")
//    private Long total;

    @Schema(description = "x轴数据")
    private List<String> xAxis;

    @Schema(description = "series数据")
    XSeriesVO<T> xSeries;
}
