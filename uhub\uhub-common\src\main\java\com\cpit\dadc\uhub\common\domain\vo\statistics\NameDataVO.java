package com.cpit.dadc.uhub.common.domain.vo.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NameDataVO<T> implements Serializable {

    @Schema(description = "流程设计id")
    private String bpmnProcessId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "激活数量")
    private List<T> activeCount;

    @Schema(description = "完成数量")
    private List<T> completeCount;

    @Schema(description = "完成数量")
    private List<T> failedCount;
}
