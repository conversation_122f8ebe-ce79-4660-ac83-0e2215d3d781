package com.cpit.dadc.uhub.common.domain.vo.statistics;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XSeriesVO<T> implements Serializable {

    @Schema(description = "x轴数据")
    private List<String> xAxis;

    @Schema(description = "series数据")
    private List<T> series;
}
