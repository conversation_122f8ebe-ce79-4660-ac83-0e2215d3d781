package com.cpit.dadc.uhub.common.domain.vo.task;

import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskState;
import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskImplementation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TaskVO {

    private String id;
    private String taskId;
    private Long key;
    private Long processDefinitionId;
    private String processInstanceId;
    private Long processDefinitionKey;
    private int processDefinitionVersion;
    private Long bpmnProcessId;
    private String jobType;
    private TaskState state;
    private Long creationTime;
    private Long completionTime;
    private LocalDateTime dueDate;
    private Integer retries;
    private String worker;
    private String flowNodeBpmnId;
    private Long flowNodeInstanceId;
    private Long processInstanceKey;
    private String assignee;
    private Boolean assigned;
    private String[] candidateGroups;
    private String[] candidateUsers;
    private String formKey;
    private String formId;
    private Long formVersion;
    private Boolean isFormEmbedded;
    private LocalDateTime followUpDate;

    private String tenantId;
    private TaskImplementation implementation;
    private Long duration;
}
