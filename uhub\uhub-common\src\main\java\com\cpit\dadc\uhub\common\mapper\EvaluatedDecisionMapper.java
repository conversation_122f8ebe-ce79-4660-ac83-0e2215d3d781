package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpit.dadc.uhub.common.domain.entity.bpm.EvaluatedDecision;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EvaluatedDecisionMapper extends BaseMapper<EvaluatedDecision> {

    List<EvaluatedDecision> findAllByDecisionEvaluationKey(Long decisionEvaluationKey);
}
