package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpit.dadc.uhub.common.domain.entity.bpm.ExecutionFlowInstance;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ExecutionFlowInstanceMapper extends BaseMapper<ExecutionFlowInstance> {

    /**
     * 根据 flowNodeInstanceId 查询 ACTIVE 状态的数据
     *
     * @param flowNodeInstanceId 流程节点实例 ID
     * @return ACTIVE 状态的数据
     */
    default ExecutionFlowInstance selectActiveByFlowNodeInstanceId(long flowNodeInstanceId) {
        LambdaQueryWrapper<ExecutionFlowInstance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExecutionFlowInstance::getFlowNodeInstanceId, flowNodeInstanceId) // 匹配 flow_node_instance_id
                .eq(ExecutionFlowInstance::getState, "ACTIVE"); // 匹配 state 为 ACTIVE
        return selectOne(queryWrapper);
    }
}