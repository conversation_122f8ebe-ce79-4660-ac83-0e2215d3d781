package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpit.dadc.uhub.common.domain.entity.bpm.FlowNodeInstance;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface FlowNodeInstanceMapper extends BaseMapper<FlowNodeInstance> {


    /**
     * 多分区游标分页查询
     */
    default List<FlowNodeInstance> selectByPartitionCursor(
            Date startTime,
            Date endTime,
            int pageSize,
            Long lastPartition,
            Long lastPosition
    ) {
        LambdaQueryWrapper<FlowNodeInstance> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .ge(FlowNodeInstance::getCreateTime, startTime)
                .lt(FlowNodeInstance::getCreateTime, endTime)
                .eq(FlowNodeInstance::isProcessed, false)
                .orderByAsc(FlowNodeInstance::getPartitionId)
                .orderByAsc(FlowNodeInstance::getPosition);

        if (lastPartition != null && lastPosition != null) {
            wrapper.and(q -> q
                    .gt(FlowNodeInstance::getPartitionId, lastPartition)
                    .or()
                    .eq(FlowNodeInstance::getPartitionId, lastPartition)
                    .gt(FlowNodeInstance::getPosition, lastPosition)
            );
        }

        Page<FlowNodeInstance> page = new Page<>(0, pageSize, false);
        return selectPage(page, wrapper).getRecords();
    }

    default List<FlowNodeInstance> selectByCreateTimeRangeWithCursor(
            Date startTime,
            Date endTime,
            int pageSize
    ) {
        LambdaQueryWrapper<FlowNodeInstance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .ge(FlowNodeInstance::getCreateTime, startTime)
                .lt(FlowNodeInstance::getCreateTime, endTime)
                .eq(FlowNodeInstance::isProcessed, false)
                .orderByAsc(FlowNodeInstance::getId); // 按主键排序保证游标稳定

        Page<FlowNodeInstance> page = new Page<>(0, pageSize);
        return selectPage(page, queryWrapper).getRecords();
    }

    /**
     * 批量标记为已处理（Lambda 方式）
     */
    default int batchMarkProcessedLambda(List<String> ids) {
        if (ids == null || ids.isEmpty()) return 0;

        LambdaUpdateWrapper<FlowNodeInstance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .set(FlowNodeInstance::isProcessed, true)
                .in(FlowNodeInstance::getId, ids);

        return update(null, updateWrapper);
    }

}
