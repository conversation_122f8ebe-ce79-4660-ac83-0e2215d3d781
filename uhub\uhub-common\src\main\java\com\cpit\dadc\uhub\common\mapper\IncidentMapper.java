package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpit.dadc.uhub.common.domain.entity.bpm.IncidentEntity;
import com.cpit.dadc.uhub.common.domain.entity.bpm.IncidentState;


import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Mapper
public interface IncidentMapper extends BaseMapper<IncidentEntity> {

    @Transactional(readOnly = true)
    List<IncidentEntity> findByProcessInstanceKey(Long processInstanceKey);

    @Transactional(readOnly = true)
    List<IncidentEntity> findByJobKey(Long jobKey);

    @Transactional(readOnly = true)
    List<IncidentEntity> findByStateIn(List<IncidentState> stateIn, Page Page);

    Long countByStateIn(List<IncidentState> stateIn);

    @Transactional(readOnly = true)
    List<IncidentEntity> findByProcessInstanceKeyAndStateIn(Long processInstanceKey, List<IncidentState> stateIn);
}
