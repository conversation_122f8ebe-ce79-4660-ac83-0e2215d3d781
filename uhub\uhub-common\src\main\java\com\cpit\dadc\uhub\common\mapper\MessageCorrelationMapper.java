package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpit.dadc.uhub.common.domain.entity.bpm.MessageCorrelation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MessageCorrelationMapper extends BaseMapper<MessageCorrelation> {

    List<MessageCorrelation> findByMessageNameAndElementInstanceKey(String messageName, Long elementInstanceKey);

    List<MessageCorrelation> findByMessageNameAndProcessDefinitionKey(String messageName, Long processDefinitionKey);

    List<MessageCorrelation> findByMessageKey(Long messageKey);
}
