package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpit.dadc.uhub.common.domain.entity.bpm.Message;
import com.cpit.dadc.uhub.common.domain.entity.bpm.MessageState;


import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    List<Message> findByStateIn(List<MessageState> stateIn, Page Page);

    Long countByStateIn(List<MessageState> stateIn);
}
