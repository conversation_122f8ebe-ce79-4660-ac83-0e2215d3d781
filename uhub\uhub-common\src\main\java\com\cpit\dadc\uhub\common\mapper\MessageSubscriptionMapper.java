package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpit.dadc.uhub.common.domain.entity.bpm.MessageSubscription;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface MessageSubscriptionMapper extends BaseMapper<MessageSubscription> {

    List<MessageSubscription> findByProcessInstanceKey(Long processInstanceKey);

    List<MessageSubscription> findByElementInstanceKey(Long elementInstanceKey);

    List<MessageSubscription> findByProcessDefinitionKeyAndElementInstanceKeyIsNull(Long processDefinitionKey);

    MessageSubscription findByElementInstanceKeyAndMessageName(Long elementInstanceKey, String messageName);

    MessageSubscription findByProcessDefinitionKeyAndMessageName(Long processDefinitionKey, String messageName);
}
