package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpit.dadc.uhub.common.domain.entity.bpm.ProcessFlowNode;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ProcessFlowNodeMapper extends BaseMapper<ProcessFlowNode> {


    default ProcessFlowNode selectFlowNode(long processDefinitionKey, String flowNodeId) {
        LambdaQueryWrapper<ProcessFlowNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessFlowNode::getProcessDefinitionKey, processDefinitionKey) // 匹配 process_definition_key
                .eq(ProcessFlowNode::getFlowNodeId, flowNodeId); // 匹配 state 为 ACTIVE
        return selectOne(queryWrapper);
    }
}
