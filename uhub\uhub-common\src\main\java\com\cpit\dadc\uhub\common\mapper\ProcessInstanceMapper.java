package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpit.dadc.uhub.common.domain.dto.process.ProcessInstanceSearchDTO;
import com.cpit.dadc.uhub.common.domain.entity.bpm.ProcessInstance;
import com.cpit.dadc.uhub.common.domain.vo.processinstance.ProcessInstanceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProcessInstanceMapper extends BaseMapper<ProcessInstance> {


    Page<ProcessInstanceVO> page(IPage<ProcessInstanceVO> page, @Param("searchDTO") ProcessInstanceSearchDTO searchDTO);

}
