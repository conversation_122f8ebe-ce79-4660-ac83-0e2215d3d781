package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpit.dadc.uhub.common.domain.entity.bpm.Process;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProcessMapper extends BaseMapper<Process> {
    IPage<Process> selectLastVersionActivePage(IPage<Process> page
            , @Param("tenantId") String tenantId
            , @Param("procDefKey") String procDefKey
            , @Param("procDefName") String procDefName
    );

}
