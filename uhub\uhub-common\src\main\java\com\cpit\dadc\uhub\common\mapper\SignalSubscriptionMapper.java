package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpit.dadc.uhub.common.domain.entity.bpm.SignalSubscription;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SignalSubscriptionMapper extends BaseMapper<SignalSubscription> {

    List<SignalSubscription> findByProcessDefinitionKey(Long processDefinitionKey);

    SignalSubscription findByProcessDefinitionKeyAndSignalName(Long processDefinitionKey, String signalName);
}
