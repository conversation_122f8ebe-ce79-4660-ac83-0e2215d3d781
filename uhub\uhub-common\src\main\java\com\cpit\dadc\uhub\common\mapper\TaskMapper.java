package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cpit.dadc.uhub.common.domain.entity.bpm.TaskState;
import com.cpit.dadc.uhub.common.domain.entity.bpm.Task;

import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface TaskMapper extends BaseMapper<Task> {

    List<Task> findByProcessInstanceKey(Long processInstanceKey);

    List<Task> findByProcessInstanceKeyAndStateIn(Long processInstanceKey, List<TaskState> stateIn);

    List<Task> findByProcessInstanceKeyAndStateInAndJobTypeIn(Long processInstanceKey, List<TaskState> stateIn, List<String> jobTypeIn);

    List<Task> findByStateIn(List<TaskState> stateIn, Page Page);

    Long countByStateIn(List<TaskState> stateIn);

    List<Task> findByJobTypeIn(List<String> jobTypeIn, Page Page);

    List<Task> findByStateInAndJobTypeIn(List<TaskState> stateIn, List<String> jobTypeIn, Page Page);

    Long countByStateInAndJobTypeIn(List<TaskState> stateIn, List<String> jobTypeIn);
}
