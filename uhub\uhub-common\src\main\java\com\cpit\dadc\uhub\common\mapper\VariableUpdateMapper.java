package com.cpit.dadc.uhub.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cpit.dadc.uhub.common.domain.entity.bpm.VariableUpdate;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Mapper
public interface VariableUpdateMapper extends BaseMapper<VariableUpdate> {

    @Transactional(readOnly = true)
    List<VariableUpdate> findByProcessInstanceKey(Long processInstanceKey);

    @Transactional(readOnly = true)
    List<VariableUpdate> findByVariableKey(Long variableKey);
}
