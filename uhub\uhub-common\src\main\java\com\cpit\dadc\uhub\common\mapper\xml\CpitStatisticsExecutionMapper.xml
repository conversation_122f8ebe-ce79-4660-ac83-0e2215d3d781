<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cpit.dadc.uhub.common.mapper.CpitStatisticsExecutionMapper">

    <!-- 定义 resultMap -->
    <resultMap id="CpitStatisticsExecutionResultMap" type="com.cpit.dadc.uhub.common.domain.entity.bpm.CpitStatisticsExecution">
        <id property="id" column="id" jdbcType="VARCHAR" />
        <result property="year" column="year" jdbcType="INTEGER" />
        <result property="month" column="month" jdbcType="SMALLINT" />
        <result property="date" column="date" jdbcType="VARCHAR" />
        <result property="bpmnProcessId" column="bpmn_process_id" jdbcType="VARCHAR" />
        <result property="processDefinitionKey" column="process_definition_key" jdbcType="BIGINT" />
        <result property="completeCount" column="complete_count" jdbcType="BIGINT" />
        <result property="activeCount" column="active_count" jdbcType="BIGINT" />
        <result property="failedCount" column="failed_count" jdbcType="BIGINT" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
    </resultMap>

    <insert id="insertOrUpdateStatisticsExecution">
        INSERT INTO cpit_statistics_execution (
        id, year, month, date, process_definition_key, bpmn_process_id,
        complete_count, active_count, failed_count, tenant_id
        ) VALUES (
        #{id}, #{year}, #{month}, #{date}, #{processDefinitionKey}, #{bpmnProcessId},
        CASE
        WHEN #{state} = 'COMPLETED' OR #{state} = 'CANCELED' THEN 1
        ELSE 0
        END,
        CASE
        WHEN #{state} = 'ACTIVE' THEN 1
        ELSE 0
        END,
        CASE
        WHEN #{state} = 'PENDING' THEN 1
        ELSE 0
        END,
        #{tenantId}
        )
        ON CONFLICT (id) DO UPDATE
        SET
        complete_count = cpit_statistics_execution.complete_count + (
        CASE
        WHEN #{state} = 'COMPLETED' OR #{state} = 'CANCELED' THEN 1
        ELSE 0
        END
        ),
        active_count = cpit_statistics_execution.active_count + (
        CASE
        WHEN #{state} = 'ACTIVE' THEN 1
        ELSE 0
        END
        ),
        failed_count = cpit_statistics_execution.failed_count + (
        CASE
        WHEN #{state} = 'PENDING' THEN 1
        ELSE 0
        END
        );
    </insert>


</mapper>