<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cpit.dadc.uhub.common.mapper.ProcessMapper">

    <!-- PROCESSDEFINITION RESULTMAP -->

    <resultMap id="processDefinitionResultMap" type="com.cpit.dadc.uhub.common.domain.entity.bpm.Process">
        <id property="id" column="id" jdbcType="VARCHAR" />
        <result property="name" column="name" jdbcType="VARCHAR" />
        <result property="processDefinitionKey" column="process_definition_key" jdbcType="VARCHAR" />
        <result property="bpmnProcessId" column="bpmn_process_id" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="bpmnXml" column="bpmn_xml" jdbcType="VARCHAR"/>
        <result property="deployTime" column="deploy_time" jdbcType="TIMESTAMP"/>
        <result property="resourceName" column="resource_name" jdbcType="VARCHAR"/>
        <result property="checksum" column="check_sum" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="startedByForm" column="started_by_form" jdbcType="TINYINT"/>
        <result property="formKey" column="form_key" jdbcType="VARCHAR"/>
        <result property="formId" column="form_id" jdbcType="VARCHAR"/>
        <result property="isFormEmbedded" column="is_form_embedded" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR" />
    </resultMap>
    <select id="selectLastVersionActivePage" resultMap="processDefinitionResultMap">
        SELECT * FROM (SELECT bpmn_process_id, MAX(version) FROM process GROUP BY bpmn_process_id) AS t, process AS p
        <where>
            t.bpmn_process_id=p.bpmn_process_id and t.max=p.version
            <if test="tenantId != null and tenantId != ''">and tenant_id=#{tenantId}</if>
            <if test="procDefKey != null and procDefKey != ''">and process_definition_key=#{procDefKey}</if>
            <if test="procDefName != null and procDefName != ''">and name=#{procDefName}</if>
        </where>
    </select>

</mapper>
