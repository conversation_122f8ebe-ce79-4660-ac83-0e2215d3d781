<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpit.dadc.uhub.common.mapper.ProcessInstanceMapper">

    <!-- 定义 ResultMap -->
    <resultMap id="ProcessInstanceResultMap" type="com.cpit.dadc.uhub.common.domain.vo.processinstance.ProcessInstanceVO">
        <id property="id" column="id" />
        <result property="key" column="key" />
        <result property="processInstanceKey" column="process_instance_key" />
        <result property="bpmnProcessId" column="bpmn_process_id" />
        <result property="processDefinitionKey" column="process_definition_key" />
        <result property="parentProcessInstanceKey" column="parent_process_instance_key" />
        <result property="parentElementInstanceKey" column="parent_element_instance_key" />
        <result property="version" column="version" />
        <result property="state" column="state" />
        <result property="startDate" column="start_date" />
        <result property="endDate" column="end_date" />
        <result property="duration" column="duration" />
        <result property="position" column="position" />
        <result property="partitionId" column="partition_id" />
        <result property="createBy" column="create_by" />
        <result property="updateBy" column="update_by" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

    <select id="page" resultMap="ProcessInstanceResultMap">
        SELECT
        COALESCE(a.id, c.id) AS id,
        a.key AS key,
        a.process_instance_key,
        a.bpmn_process_id,
        a.process_definition_key,
        a.parent_process_instance_key,
        a.parent_element_instance_key,
        a.version,
        CASE
        WHEN c.state = 'COMPLETED' THEN c.state
        ELSE a.state
        END AS state,
        a.start_date,
        c.end_date,
        CASE
        WHEN a.start_date IS NOT NULL AND c.end_date IS NOT NULL THEN c.end_date - a.start_date
        ELSE NULL
        END AS duration,
        COALESCE(a.position, c.position) AS position,
        COALESCE(a.partition_id, c.partition_id) AS partition_id,
        COALESCE(a.create_by, c.create_by) AS create_by,
        COALESCE(a.update_by, c.update_by) AS update_by,
        COALESCE(a.create_time, c.create_time) AS create_time,
        COALESCE(a.update_time, c.update_time) AS update_time,
        COALESCE(a.tenant_id, c.tenant_id) AS tenant_id
        FROM
        act_process_instance a
        LEFT JOIN
        act_process_instance c
        ON a.process_instance_key = c.process_instance_key
        AND c.state = 'COMPLETED'
        WHERE
        a.state = 'ACTIVE'
        <if test="searchDTO.bpmnProcessId != null and searchDTO.bpmnProcessId != ''">
            AND a.bpmn_process_id = #{searchDTO.bpmnProcessId}
        </if>
        <if test="searchDTO.processInstanceKey != null and searchDTO.processInstanceKey != ''">
            AND a.process_instance_key = #{searchDTO.processInstanceKey}
        </if>
        <if test="searchDTO.processDefinitionKey != null and searchDTO.processDefinitionKey != ''">
            AND a.process_definition_key = #{searchDTO.processDefinitionKey}
        </if>
        <if test="searchDTO.startDate != null and searchDTO.startDate != ''">
            AND a.start_date >= #{searchDTO.startDate}
        </if>
        <if test="searchDTO.endDate != null and searchDTO.endDate != ''">
            AND a.start_date &lt;= #{searchDTO.endDate}
        </if>
        <if test="searchDTO.state != null and searchDTO.state != ''">
            AND a.state = #{searchDTO.state}
        </if>
        <if test="searchDTO.isMine != null and searchDTO.isMine">
            AND a.create_by = #{searchDTO.createBy}
        </if>
        ORDER BY a.create_time DESC
    </select>

</mapper>
